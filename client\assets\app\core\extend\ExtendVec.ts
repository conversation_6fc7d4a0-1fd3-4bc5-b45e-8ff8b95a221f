
/**
 * Vec2扩展方法
 */

import { v2, v3, Vec2, Vec3 } from "cc"

Vec2.prototype.ID = function () {
    return this.x + '_' + this.y
}

// 拼接字符串
Vec2.prototype.Join = function (separator: string = ','): string {
    return this.x + separator + this.y
}

Vec2.prototype.toVec3 = function (): Vec3 {
    this['z'] = 0
    return this
}

Vec2.prototype.newVec3 = function (): Vec3 {
    return v3(this.x, this.y, 0)
}

Vec2.prototype.toJson = function (): any {
    return { x: this.x, y: this.y }
}

// 翻转
Vec2.prototype.FlipX = function (): Vec2 {
    let x = this.x
    this.x = this.y
    this.y = x
    return this
}

Vec2.prototype.floor = function () {
    this.x = Math.floor(this.x)
    this.y = Math.floor(this.y)
    return this
}

Vec2.prototype.ceil = function () {
    this.x = Math.ceil(this.x)
    this.y = Math.ceil(this.y)
    return this
}

// 相加的长度
Vec2.prototype.Length = function () {
    return Math.abs(this.x) + Math.abs(this.y)
}

// 自己想乘
Vec2.prototype.SelfMul = function () {
    return this.x * this.y
}

// 转成index
Vec2.prototype.toIndex = function (size: Vec2) {
    if (this.x < 0 || this.x >= size.x || this.y < 0 || this.y >= size.y) {
        return -1
    }
    return this.y * size.x + this.x
}

/**
 * Vec3扩展方法
 */

// 拼接字符串
Vec3.prototype.Join = function (separator: string = ','): string {
    return this.x + separator + this.y + separator + this.z
}

Vec3.prototype.toVec2 = function (): Vec2 {
    return this
}

Vec3.prototype.newVec2 = function (): Vec2 {
    return v2(this.x, this.y)
}
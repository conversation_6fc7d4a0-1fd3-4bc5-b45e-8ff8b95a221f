import { Layers, _decorator } from "cc";
import BaseLayerCtrl from "../base/BaseLayerCtrl";
import WindCtrlMgr from "../manage/WindCtrlMgr";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property } = _decorator;

@ccclass
export default class WindLayerCtrl extends BaseLayerCtrl {

    private ctrlMgr: WindCtrlMgr = null!

    public listenEventMaps() {
        return [
            { [CoreEventType.GOTO_WIND]: this.onGotoWind },
            { [CoreEventType.PRELOAD_WIND]: this.onPreloadWind },
            { [CoreEventType.CLEAN_CACHE_WIND]: this.onCleanCacheWind },
        ]
    }

    public onCreate() {
        this.node.layer = Layers.Enum.DEFAULT
    }

    public onClean() {

    }

    public setCtrlMgr(mgr: WindCtrlMgr) {
        this.ctrlMgr = mgr
        this.ctrlMgr.node = this.node
    }

    // 获取当前场景
    public getCurrWind() {
        return this.ctrlMgr.currWind
    }

    private onGotoWind(key: string, complete?: Function, ...params: any) {
        this.ctrlMgr.goto(key, ...params).then(() => complete && complete())
    }

    private onPreloadWind(key: string, complete?: Function, progress?: (percent: number) => void) {
        this.ctrlMgr.preLoad(key, progress, complete)
    }

    // 清理所有缓存中的wind
    private onCleanCacheWind() {
        this.ctrlMgr.cleanCacheWind()
    }
}
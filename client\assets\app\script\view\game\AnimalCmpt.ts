import { _decorator, Component, Node } from "cc";
import AnimalObj from "../../model/game/AnimalObj";
import FrameAnimationCmpt from "../cmpt/FrameAnimationCmpt";
import { getAnimalFrameAnimConf } from "../../common/config/AnimalFrameAnimConf";
import GameModel from "../../model/game/GameModel";
import { AnimalState } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import AttrBarCmpt from "./AttrBarCmpt";

const { ccclass, property } = _decorator;

// 一个动物
@ccclass
export default class AnimalCmpt extends Component {

    private key: string = ''

    public data: AnimalObj = null
    private body: Node = null
    private animNode: Node = null
    private animCmpt: FrameAnimationCmpt = null
    private currAnimName: string = ''
    private attrBar: AttrBarCmpt = null

    private preStateUid: string = ''

    public init(data: AnimalObj, key: string) {
        this.data = data
        this.key = key
        this.body = this.FindChild('body')
        // this.body.scale.set(this.data.isFriendly() ? 1 : -1, 1)
        this.animNode = this.FindChild('body/anim')
        this.animCmpt = this.animNode.getComponent(FrameAnimationCmpt)
        this.animCmpt.setUpdateModel(GameModel.ins())
        this.animCmpt.init(getAnimalFrameAnimConf(data.getViewId()), key).then(() => this.isValid && this.playAnimation('idle'))
        this.loadAttrBar()
        return this
    }

    public resync(data: any) {
        return this
    }

    public clean(release?: boolean) {
        this.animCmpt.clean()
        this.node.destroy()
        release && assetsMgr.releaseTempRes(this.data?.getPrefabUrl(), this.key)
        this.data = null
    }

    private async loadAttrBar() {
        const it = await nodePoolMgr.get('animal/ATTR_BAR', this.key)
        if (!this.isValid || !this.data) {
            return nodePoolMgr.put(it)
        }
        it.parent = this.node
        it.zIndex = 10
        it.active = true
        this.attrBar = it.getComponent(AttrBarCmpt).init(this.data)
    }

    public get uid(): string { return this.data?.uid || '' }

    update(dt: number) {
        if (!this.data) {
            return
        }
        this.updateState()
    }

    // 播放动画
    public playAnimation(name: string, cb?: Function, startTime?: number) {
        this.currAnimName = name
        this.animCmpt?.play(name, cb, startTime)
    }

    // 同步状态信息
    private updateState() {
        if (!this.data?.state || this.preStateUid === this.data.state.uid) {
            return
        }
        this.preStateUid = this.data.state.uid
        const state = this.data.state.type, data = this.data.state.data
        // cc.log('updateState', this.uid, this.point.ID(), AnimalState[state])
        // this.data.actioning = this.data.actioning || (state !== AnimalState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动
        if (state === AnimalState.STAND) { //待机
            this.doStand()
        } else if (state === AnimalState.ATTACK) { //攻击
            this.doAttack(data)
        } else if (state === AnimalState.HIT) { //受击
            this.doHit(data)
        } else {
            this.playAnimation('idle')
        }
    }

    // 待机
    private doStand() {
        const animName = this.animCmpt?.playAnimName
        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle
            this.playAnimation('idle')
        }
        this.attrBar?.reset()
    }

    // 攻击
    private doAttack(data: any) {
        const currAttackTime = data.currAttackTime ?? 0
        const suffix = data.instabilityAttackIndex || ''
        this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime)
    }

    // 受击
    private doHit(data: any) {
        let damage = data.damage ?? 0
        const isDie = this.data.isDie()
        const sound = data.sound //受击音效
        const uid = this.uid
        this.attrBar?.play()
        if (damage === 0) {
            return this.playAnimation('idle')
        }
        let animName = 'hit'
        if (isDie) {
            animName = 'die'
            // this.playSFXByKey('die_sound')
        } else if (sound) {
            // this.playSFX(sound)
        }
        this.playAnimation(animName, () => {
            if (isDie) {
                eventCenter.emit(EventType.REMOVE_ANIMAL, uid, false)
            } else if (this.isValid) {
                this.playAnimation('idle')
            }
        })
    }

    // 直接死亡
    private doDie(data: any) {

    }
}
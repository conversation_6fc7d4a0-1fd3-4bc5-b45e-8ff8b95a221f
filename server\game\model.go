package game

import (
	"casrv/server/common/pb"
	rds "casrv/server/common/redis"
	ut "casrv/utils"
	"casrv/utils/array"
	"encoding/json"

	"github.com/huyangv/vmqant/log"
)

type Item struct {
	Id int32 `json:"id"`
	Lv int8  `json:"lv"`
}

type Animal struct {
	Item
}

func (this *Item) ToPb() *pb.Item     { return &pb.Item{Id: this.Id, Lv: int32(this.Lv)} }
func (this *Animal) ToPb() *pb.Animal { return &pb.Animal{Id: this.Id, Lv: int32(this.Lv)} }

func NewItem(id int32) *Item {
	return &Item{Id: id}
}

// 商店信息
type ShopInfo struct {
	Id         int32   `json:"id"`         //商店id
	State      int8    `json:"state"`      //商店状态
	Items      []*Item `json:"items"`      //物品列表
	SelectItem *Item   `json:"selectItem"` //选择的物品
}

func (this *ShopInfo) ToPb() *pb.ShopInfo {
	return &pb.ShopInfo{Id: this.Id, Items: array.Map(this.Items, func(m *Item, _ int) *pb.Item { return m.ToPb() })}
}

// 其他玩家的信息
type PlayerInfo struct {
	Animals  []*Animal `json:"animals"` //动物列表
	Bags     []*Item   `json:"bags"`    //背包
	UID      string    `json:"uid"`
	Nickname string    `json:"nickname"`
	RoleId   int32     `json:"roleId"`   //角色id
	Day      int32     `json:"day"`      //天数
	HP       []int32   `json:"hp"`       //生命
	WinCount int32     `json:"winCount"` //胜利次数
}

func (this *PlayerInfo) ToPb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		Animals:  array.Map(this.Animals, func(m *Animal, _ int) *pb.Animal { return m.ToPb() }),
		Bags:     array.Map(this.Bags, func(m *Item, _ int) *pb.Item { return m.ToPb() }),
		Uid:      this.UID,
		Nickname: this.Nickname,
		RoleId:   this.RoleId,
		Day:      this.Day,
		Hp:       array.Clone(this.HP),
		WinCount: this.WinCount,
	}
}

func (this *PlayerInfo) ToBasePb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		Day:      this.Day,
		Hp:       array.Clone(this.HP),
		WinCount: this.WinCount,
	}
}

// 游戏数据
type GameData struct {
	Player      *PlayerInfo `json:"player"`      // 自己的信息
	OtherPlayer *PlayerInfo `json:"otherPlayer"` // 其他玩家信息
	Shop        *ShopInfo   `json:"shopInfo"`    // 商店信息
	CreateTime  int64       `json:"createTime"`  // 创建时间
}

func (this *GameData) ToPb() *pb.GameData {
	var otherPlayer *pb.PlayerInfo = nil
	if this.OtherPlayer != nil {
		otherPlayer = this.OtherPlayer.ToPb()
	}
	return &pb.GameData{
		Player:      this.Player.ToPb(),
		Shop:        this.Shop.ToPb(),
		OtherPlayer: otherPlayer,
	}
}

// 获取游戏数据
func GetGameData(uid string) *GameData {
	jsonStr, err := rds.RdsHGet(rds.RDS_GAME_DATA_KEY+uid, "data")
	if err != nil || jsonStr == "" {
		return nil
	}
	// 将JSON字符串反序列化为GameData对象
	var gameData GameData
	if err := json.Unmarshal([]byte(jsonStr), &gameData); err != nil {
		log.Error("GetGameData unmarshal error: %v", err)
		return nil
	}
	return &gameData
}

// 保存游戏数据到redis
func SaveGameData(uid string, data *GameData) error {
	// 将GameData序列化为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		log.Error("SaveGameData marshal error: %v", err)
		return err
	}
	// 保存到redis hash中
	return rds.RdsHSet(rds.RDS_GAME_DATA_KEY+uid, "data", string(jsonBytes))
}

// 创建游戏信息
func CreateGame(uid string, nickname string, roleId int32) *GameData {
	data := &GameData{CreateTime: ut.Now()}
	// 自己的数据
	data.Player = &PlayerInfo{
		UID:      uid,
		Nickname: nickname,
		RoleId:   roleId,
		Animals:  []*Animal{},
		Bags:     []*Item{},
		Day:      1, //从第一天开始
		HP:       []int32{100, 100},
		WinCount: 0,
	}
	// 随机三个东西 变异幼年动物 金币和收益 祝福
	data.Shop = &ShopInfo{
		Id:    BEGIN_GAME_SHOP_ID,
		Items: []*Item{},
	}
	// 保存到redis
	SaveGameData(uid, data)
	return data
}

import { log } from "cc";
import AnimalObj from "../game/AnimalObj";
import BehaviorTree from "./BehaviorTree";
import FSPController from "./FSPController";
import { AnimalState } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";

// 一个战斗单位
export default class FSPFighter {

    public ctrl: FSPController = null

    public entity: AnimalObj = null
    public attackIndex: number = 0 //出手顺序
    public roundCount: number = 0

    public behavior: BehaviorTree = null //行为树
    public blackboard: any = null //携带行为树数据

    public init(animal: AnimalObj, data: any, ctrl: FSPController) {
        this.entity = animal
        this.attackIndex = data.attackIndex ?? 0
        this.ctrl = ctrl
        this.blackboard = { 0: {} }
        this.behavior = new BehaviorTree().load(this)
        return this
    }

    public getUid() { return this.entity.uid }
    public getId() { return this.entity.id }
    public getCamp() { return this.entity.camp }
    public getIndex() { return this.entity.index }
    public isDie() { return this.entity.isDie() }

    public getMaxHp() {
        return this.entity.maxHp
    }

    public getBlackboard(id: number): any {
        let data = this.blackboard[id]
        if (!data) {
            data = this.blackboard[id] = {}
        }
        return data
    }

    public cleanBlackboard(...retainKeys: string[]) {
        const retainMap = {}
        const data = this.blackboard[0]
        if (data) {
            retainKeys.forEach(k => retainMap[k] = data[k])
        }
        this.blackboard = { 0: retainMap }
    }

    // 是否回合结束了
    public isRoundEnd() {
        return !!this.getBlackboard(0)['isRoundEnd'] || this.isDie()
    }

    // 设置回合结束
    public setRoundEnd() {
        this.getBlackboard(0)['isRoundEnd'] = true
    }

    // 回合开始
    public beginAction() {
        this.blackboard = { 0: {} }
        log(this.ctrl.getCurrentFrameIndex() + ' >>>>>>>>> ' + this.getId() + '(' + this.getIndex() + ') [' + this.getUid() + '] ' + this.attackIndex + ' ' + this.getCamp() + ' ' + this.ctrl.getRandom().seed)
    }

    public endAction() {
        this.roundCount += 1
        this.cleanBlackboard()
        log(this.ctrl.getCurrentFrameIndex() + ' <<<<<<<<< ' + this.getId() + '(' + this.getIndex() + ') [' + this.getUid() + '] ')
        log('-')
    }

    // 刷新buff
    public updateBuff() {

    }

    // 执行行为树
    public behaviorTick(dt: number) {
        this.behavior.tick(dt)
    }

    public getAttack() {
        return this.entity.attack
    }

    // 添加状态
    public changeState(state: AnimalState, data?: any) {
        const uid = this.getUid()
        this.entity.changeState(state, data)
        if (state === AnimalState.HIT) {
            let damage = data.damage ?? 0
            const isCrit = !!data.isCrit //暴击
            const heal = data.heal ?? 0 //回复
            eventCenter.emit(EventType.PLAY_FLUTTER_HP, { uid, value: -damage, heal, isCrit })
        }
    }

    public onHit(damage: number) {
        this.entity.curHp -= damage
        log(this.ctrl.getCurrentFrameIndex() + ' OnHit ' + this.entity.id + '(' + this.entity.index + ') 🗡' + damage + ' ' + this.entity.curHp + '/' + this.getMaxHp() + ' [' + this.entity.uid + ']')
    }
}
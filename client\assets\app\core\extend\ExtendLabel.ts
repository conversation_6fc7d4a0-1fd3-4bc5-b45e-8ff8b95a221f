
/**
 * Label扩展方法
 */

import { Label, RichTextComponent } from "cc"
import LocaleLabel from "../component/LocaleLabel"
import LocaleRichText from "../component/LocaleRichText"

Label.prototype.setLocaleKey = function (key: string, ...params: any[]) {
    const localeLabel = this.Component(LocaleLabel)
    if (localeLabel) {
        localeLabel.setKey(key, ...params)
    } else {
        this.string = key
    }
}

RichTextComponent.prototype.setLocaleKey = function (key: string, ...params: any[]) {
    const localeLabel = this.Component(LocaleRichText)
    if (localeLabel) {
        localeLabel.setKey(key, ...params)
    } else {
        this.string = key
    }
}
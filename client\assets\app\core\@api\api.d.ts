
declare var wx: any
declare var qq: any

type ProcessCallback = (completedCount: number, totalCount: number, item?: any) => void;
type LoadProgressCallback = (percent: number) => void;

type Point = {
    x: number;
    y: number;
}

type EventItem = {
    callback: Function;
    target: any;
}

// 加载pnl信息
type LoadPnlInfo = {
    id: number;
    name: string; //传入名
    url: string; //实际pnl路径
    params?: any[]; //参数
}

// 事件中心
declare var eventCenter: {
    emit(type: number | string, ...params: any): void;
    get(type: number | string, ...params: any): any;
    req(type: number | string, ...params: any): Promise<any>;
    on(type: number | string, callback: Function, target?: any): void;
    once(type: number | string, callback: Function, target?: any): void;
    wait(type: number | string): Promise<any>;
    off(type: number | string, callback?: Function, target?: any): void;
    clean(): void;
}

// 本地存储管理
declare var storageMgr: {
    loadString(key: string): string | null;
    saveString(key: string, val: string): void;
    loadNumber(key: string): number | null;
    saveNumber(key: string, val: number): void;
    loadBool(key: string): boolean | null;
    saveBool(key: string, val: boolean): void;
    loadJson(key: string): any;
    saveJson(key: string, val: any): void;
    reset(): void;
    clear(): void;
}

// 日志
declare var logger: {
    info(msg: any | string, ...subst: any[]): void;
    error(msg: any | string, ...subst: any[]): void;
}

// 配置表结构
type JsonConfData = {
    datas: any[];
    _dataIdMap: any;
    getById(id: string | number): any;
    get(key: string, value: any): any[];
}

// 资源管理
declare namespace assetsMgr {

    // @ts-ignore
    import { SpriteFrame, Prefab, AudioClip, Material, Font, Asset, __private } from "cc";

    export var debug: boolean;

    // 初始化
    export function init(onProgess?: (percent: number) => void): Promise<void>;

    export function clean(): void;

    /**
     * 获取配置表数据
     * @param name 
     */
    export function getJson(name: string): JsonConfData;

    /**
     * 获取配置表数据2
     * @param name 
     * @param id 
     */
    export function getJsonData(name: string, id: string | number): any;

    /**
     * 获取全局图片
     * @param name 
     */
    export function getImage(name: string): SpriteFrame;

    /**
     * 获取全局预制体
     * @param name 
     */
    export function getPrefab(name: string): Prefab;

    /**
     * 获取声音
     * @param name 
     */
    export function getAudio(name: string): AudioClip;

    /**
     * 获取材质
     * @param name
     */
    export function getMaterial(name: string): Material;

    /**
     * 获取字体
     * @param name 
     */
    export function getFont(name: string): Font;

    /**
     * 转换文本
     * @param key 
     * @param params 
     */
    export function lang(key: string, ...params: any[]): string;

    /**
     * 刷新多语言文本参数
     * @param params 
     * @param lang 
     */
    export function updateLangParams(params: any[], lang?: string): any[];

    /**
     * 加载内存资源
     * 会直接存在内存中永久不会删除
     * @param name 
     * @param type 
     */
    export function loadCommonRes(name: string, type: typeof Asset): Promise<any>;

    /**
     * 加载临时资源 (需要手动释放)
     * 会先从内存中寻找 如果有就会直接获取 没有将加载进来放入内存 缓存
     * @param name 
     * @param type 
     * @param tag 
     */
    export function loadTempRes<T extends Asset>(name: string, type: __private.__types_globals__Constructor<T>, tag?: string): Promise<T>;
    export function loadTempRseDir<T extends Asset>(key: string, type: __private.__types_globals__Constructor<T>, tag?: string): Promise<T[]>;

    /**
     * 加载远程图片 (需要手动释放)
     * @param url
     * @param ext 图片后缀 （.png .jpg）
     * @param tag
     */
    export function loadRemote(url: string, ext: string, tag?: string): Promise<SpriteFrame>;

    /**
     * 释放临时资源
     * @param name 
     * @param tag 
     */
    export function releaseTempRes(name: string, tag?: string): void;
    export function releaseTempAsset(name: string): void;

    /**
     * 释放所有标记的临时资源
     * @param tag 
     */
    export function releaseTempResByTag(tag: string): void;

    /**
     * 加载一次性资源 不缓存
     * 注意：需要和releaseOnceRes成对使用
     * @param url 
     * @param type 
     */
    export function loadOnceRes(url: string, type: typeof Asset): Promise<any>;

    export function releaseOnceRes(url: string, type: typeof Asset): void;

    /**
     * 切换语言时 要切换字体
     * @param lang 
     */
    export function changeLangJson(lang: string): void;
}

// 声音
declare namespace audioMgr {

    // @ts-ignore
    import { AudioClip } from "cc";

    export var bgmVolume: number;
    export var sfxVolume: number;

    // 初始化
    export function init(): void;
    // 暂停所有声音
    export function pauseAll(): void;
    // 恢复所有声音
    export function resumeAll(): void;
    //
    export function stopAll(): void;
    export function clean(): void;

    export function pauseBGM(): void;
    export function resumeBGM(): void;

    export function resetBGMRecord(): void;

    export function fadeOutBGM(duration: number, stop: boolean): Promise<void>;
    export function fadeInBGM(duration: number, ratio?: number): Promise<void>;

    export function playFadeInBGM(url: string, volume?: number): void;

    /**
     * 加载声音
     * @param urls 
     */
    export function load(urls: string | string[]): Promise<void>;

    /**
     * 预加载
     * @param url 
     */
    export function preload(url: string): Promise<void>;

    /**
     * 加载声音 根据模块来
     * @param mod
     */
    export function loadByMod(mod?: string): Promise<void>;

    /**
     * 释放单个声音
     * @param val 
     */
    export function release(val: string): void;

    /**
     * 释放对应mod的声音
     * @param mod 
     */
    export function releaseByMod(mod?: string): void;

    /**
     * 释放所有声音
     */
    export function releaseAll(): void;

    /**
     * 播放背景音乐
     * @param url 
     */
    export function playBGM(url: string, volume?: number): void;

    /**
     * 停止播放背景音乐
     */
    export function stopBGM(): void;

    /**
     * 播放音效
     * @param url 
     * @param opts { volume?: number, startTime?: number, loop?: boolean, onComplete?: Function, tag?: string }
     */
    export function playSFX(url: string, opts?: { volume?: number, startTime?: number, loop?: boolean, onComplete?: Function, tag?: string }): Promise<number>;

    /**
     * 停止播放音效
     * @param val 
     * @param tag 
     */
    export function stopSFX(val: number | string | AudioClip, tag?: string): void;

    export function fadeOutSFX(duration: number, val: number | string | AudioClip, tag?: string): void;

    /**
     * 设置临时的背景音乐声音
     * @param val 
     */
    export function setTempBgmVolume(val: number): void;
}

// 对象池管理
declare namespace nodePoolMgr {

    // @ts-ignore
    import { Node } from "cc";

    /**
     * 获取节点
     * @param url 基于 resources/tmp/prefab 下面的路径
     * @param key 
     */
    export function get(url: string, key?: string): Promise<Node>;

    /**
     * 回收一个节点
     * @param node 
     */
    export function put(node: Node): void;

    /**
     * 释放所有缓存
     */
    export function releaseAll(): void;

    /**
     * 释放指定模块的缓存
     */
    export function releaseByTag(key: string): void;

    /**
     * 清理指定模块的 items列表
     * @param key 
     * @param count 清理数量
     */
    export function removeItemsByTag(key: string, count?: number): void;

    /**
     * 清理使用的和没有使用的 items列表
     * @param key 
     */
    export function cleanUseAndRemoveItemsByTag(key: string): void;
}
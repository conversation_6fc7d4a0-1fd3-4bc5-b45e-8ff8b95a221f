import { loader } from "../utils/ResLoader"

type SfxInfo = {
    id: number;
    url: string;
    tag: string;
}

/**
 * 音频管理中心
 */
class AudioMgr {

    // private __bgmVolume: number = 1 //背景音乐 音量
    // private __sfxVolume: number = 1 //特效音乐 音量
    // private __isPause: boolean = false

    // private __bgmAudioID: number = -1 //背景音乐 ID
    // private __bgmVolumeRatio: number = 1 //背景比例
    // private __caches: AudioAsset[] = []

    // private __sfxs_infos: SfxInfo[] = [] //当前播放的特效列表

    // private __stateTypeMap: {} = {}

    // private __bgmUrl: string = ""
    // private __bgmTimeMap: {} = {}

    // public init() {
    //     const bgmV = storageMgr.loadNumber('bgm_volume')
    //     this.__bgmVolume = bgmV !== null ? bgmV : 1
    //     const sfxV = storageMgr.loadNumber('sfx_volume')
    //     this.__sfxVolume = sfxV !== null ? sfxV : 1
    //     logger.info('bgmVolume:', this.__bgmVolume, ' sfxVolume:', this.__sfxVolume)
    //     // 微信小游戏
    //     if (ut.isWechatGame()) {
    //         wx.onAudioInterruptionBegin(this.onAudioInterruptionBegin.bind(this))
    //         wx.onAudioInterruptionEnd(this.onAudioInterruptionEnd.bind(this))
    //     }
    // }

    // public clean() {
    //     if (ut.isWechatGame()) {
    //         wx.offAudioInterruptionBegin(this.onAudioInterruptionBegin.bind(this))
    //         wx.offAudioInterruptionEnd(this.onAudioInterruptionEnd.bind(this))
    //     }
    // }

    // private onAudioInterruptionBegin() {
    //     this.pauseAll()
    // }

    // private onAudioInterruptionEnd() {
    //     this.resumeAll()
    // }

    // public pauseAll() {
    //     this.__isPause = true
    //     this.__sfxs_infos.forEach(m => (m.id !== undefined) && cc.audioEngine.pause(m.id))
    //     if (this.__bgmAudioID >= 0) audioEngine.pause(this.__bgmAudioID)
    // }

    // public resumeAll() {
    //     this.__isPause = false
    //     if (this.__bgmVolume > 0 && this.__bgmAudioID >= 0) {
    //         cc.audioEngine.resume(this.__bgmAudioID)
    //     }
    //     if (this.__sfxVolume > 0) {
    //         this.__sfxs_infos.forEach(m => (m.id !== undefined) && cc.audioEngine.resume(m.id))
    //     }
    // }

    // public stopAll() {
    //     cc.audioEngine.stopAll()
    // }

    // public get bgmVolume() {
    //     return this.__bgmVolume
    // }
    // public set bgmVolume(val: number) {
    //     this.__bgmVolume = val
    //     storageMgr.saveNumber('bgm_volume', val)
    //     if (this.__bgmAudioID >= 0) {
    //         if (val <= 0) {
    //             cc.audioEngine.pause(this.__bgmAudioID)
    //         } else {
    //             cc.audioEngine.resume(this.__bgmAudioID)
    //         }
    //         cc.audioEngine.setVolume(this.__bgmAudioID, val * this.__bgmVolumeRatio)
    //     }
    // }

    // public setTempBgmVolume(val: number) {
    //     if (this.__bgmAudioID >= 0) {
    //         cc.audioEngine.setVolume(this.__bgmAudioID, val * this.__bgmVolumeRatio)
    //     }
    // }

    // public get sfxVolume() {
    //     return this.__sfxVolume
    // }
    // public set sfxVolume(val: number) {
    //     this.__sfxVolume = val
    //     storageMgr.saveNumber('sfx_volume', val)
    //     this.__sfxs_infos.forEach(m => cc.audioEngine.setVolume(m.id, val))
    // }

    // // 加载单个声音
    // public async load(urls: string | string[]) {
    //     urls = Array.isArray(urls) ? urls : [urls]
    //     for (let i = 0, l = urls.length; i < l; i++) {
    //         const asset = await this.__load(urls[i])
    //         asset && this.__addAudio(asset.mod, asset.url, asset.audio)
    //     }
    // }

    // // 预加载    
    // public async preload(url: string) {
    //     const data = await this.__load(url)
    //     if (data.audio) {
    //         this.__addAudio(data.mod, data.url, data.audio)
    //     } else {
    //         cc.error('load error url=', url)
    //     }
    // }

    // // 加载对于模块的音效
    // public async loadByMod(mod?: string) {
    //     mod = mod || mc.currWindName
    //     const url = 'sound/' + mod
    //     const audios: cc.AudioClip[] = await loader.loadResDir(url, cc.AudioClip)
    //     audios.forEach(audio => this.__addAudio(mod, url + '/' + audio.name, audio))
    // }

    // // 释放音效
    // public release(val: string) {
    //     let asset = this.__getForCache(val)
    //     if (asset) {
    //         this.__caches.remove('url', asset.url)
    //         if (!this.__sfxs_infos.remove('url', asset.url)) {
    //             this.__sfxs_infos.remove('url', asset.audio.nativeUrl)
    //         }
    //         loader.releaseRes(asset.url, cc.AudioClip)
    //     }
    // }

    // // 释放对应mod的音效
    // public releaseByMod(mod?: string) {
    //     mod = mod || mc.currWindName
    //     for (let i = this.__caches.length - 1; i >= 0; i--) {
    //         const asset = this.__caches[i]
    //         if (asset.mod === mod) {
    //             this.__caches.splice(i, 1)
    //             if (!this.__sfxs_infos.remove('url', asset.url)) {
    //                 this.__sfxs_infos.remove('url', asset.audio.nativeUrl)
    //             }
    //             loader.releaseAsset(asset.url, cc.AudioClip)
    //         }
    //     }
    // }

    // // 释放所有声音
    // public releaseAll() {
    //     while (this.__caches.length > 0) {
    //         loader.releaseAsset(this.__caches.pop().url, cc.AudioClip)
    //     }
    //     this.__sfxs_infos.length = 0
    // }

    // // 添加音效
    // private __addAudio(mod: string, url: string, audio: cc.AudioClip) {
    //     if (!this.__caches.some(m => m.url === url)) {
    //         this.__caches.push({ mod: mod, url: url, audio: audio })
    //     }
    // }

    // // 加载
    // private async __load(val: string) {
    //     let [wind, name] = val.split('/')
    //     let audio: cc.AudioClip = null, mod: string = '', url: string = ''
    //     if (!name) {// 没有输入模块的情况下 先默认从当前模块找
    //         mod = mc.currWindName
    //         url = `sound/${mod}/${wind}`
    //         loader.error = false
    //         audio = await loader.loadRes(url, cc.AudioClip)
    //         loader.error = true
    //         if (!audio) {// 如果没有就从common里面找
    //             mod = 'common'
    //             url = `sound/${mod}/${wind}`
    //             audio = await loader.loadRes(url, cc.AudioClip)
    //         }
    //     } else {
    //         mod = wind
    //         url = `sound/${mod}/${name}`
    //         audio = await loader.loadRes(url, cc.AudioClip)
    //     }
    //     return { mod, url, audio }
    // }

    // // 获取缓存音效
    // private __getForCache(val: string) {
    //     let [wind, name] = val.split('/')
    //     let asset: AudioAsset = null, url: string = ''
    //     if (!name) {
    //         url = `sound/${mc.currWindName}/${wind}`
    //         asset = this.__caches.find(m => m.url === url)
    //         if (!asset) {
    //             url = `sound/common/${wind}`
    //             asset = this.__caches.find(m => m.url === url)
    //         }
    //     } else {
    //         url = `sound/${wind}/${name}`
    //         asset = this.__caches.find(m => m.url === url)
    //     }
    //     return asset
    // }

    // // 获取音效
    // private __getAudio(val: string) {
    //     let audio = assetsMgr.getAudio(val)
    //     if (audio) {
    //         return audio
    //     }
    //     let asset = this.__getForCache(val)
    //     return asset ? asset.audio : null
    // }

    // // 播放音乐
    // public playBGM(url: string | cc.AudioClip, volume: number = 1) {
    //     if (!url) {
    //         return
    //     }
    //     this.stopBGM()
    //     if (url instanceof cc.AudioClip) {
    //         return this.__playBGM(url, volume)
    //     }
    //     let audio = this.__getAudio(url)
    //     if (audio) {// 播放
    //         this.__playBGM(audio, volume)
    //     } else {
    //         this.__load(url).then(m => {
    //             if (m.audio) {
    //                 this.__addAudio(m.mod, m.url, m.audio)
    //                 this.__playBGM(m.audio, volume)
    //             } else {
    //                 cc.error('playBGM error url=', url)
    //             }
    //         })
    //     }
    // }

    // // 播放音乐
    // public playFadeInBGM(url: string, volume: number = 1) {
    //     if (!url) {
    //         return
    //     }
    //     this.__fadeOutBGM(this.__bgmAudioID, this.__bgmUrl, 0.25, true)
    //     this.__bgmUrl = url
    //     let audio = this.__getAudio(url)
    //     if (audio) {// 播放
    //         this.__playBGM(audio, volume)
    //         this.__fadeInBGM(this.__bgmAudioID, 0.25)
    //     } else {
    //         this.__load(url).then(m => {
    //             if (m.audio) {
    //                 this.__addAudio(m.mod, m.url, m.audio)
    //                 this.__playBGM(m.audio, volume)
    //             } else {
    //                 cc.error('playBGM error url=', url)
    //             }
    //         })
    //     }
    // }

    // private __playBGM(audio: cc.AudioClip, volume: number) {
    //     this.__bgmVolumeRatio = volume
    //     this.__bgmAudioID = cc.audioEngine.play(audio, true, this.bgmVolume * this.__bgmVolumeRatio)
    //     if (this.bgmVolume === 0 || this.__isPause) {
    //         cc.audioEngine.pause(this.__bgmAudioID)
    //     }
    //     if (this.__bgmUrl !== "") {
    //         let info = this.__bgmTimeMap[this.__bgmUrl]
    //         if (info) {
    //             let passTime = Date.now() - info.stopTime
    //             if (passTime < 1000 * 150) {//2分半内才会继续进度播放
    //                 cc.audioEngine.setCurrentTime(this.__bgmAudioID, this.__bgmTimeMap[this.__bgmUrl].curTime)
    //             }
    //             else {
    //                 delete this.__bgmTimeMap[this.__bgmUrl]
    //             }
    //         }
    //     }
    // }

    // public stopBGM() {
    //     if (this.__bgmAudioID >= 0) {
    //         cc.audioEngine.stop(this.__bgmAudioID)
    //         this.__bgmAudioID = -1
    //     }
    // }

    // public pauseBGM() {
    //     this.__bgmAudioID >= 0 && cc.audioEngine.pause(this.__bgmAudioID)
    // }

    // public resumeBGM() {
    //     this.__bgmAudioID >= 0 && cc.audioEngine.resume(this.__bgmAudioID)
    // }

    // public resetBGMRecord() {
    //     this.__bgmTimeMap = {}
    // }

    // public async fadeOutBGM(duration: number, stop: boolean) {
    //    return this.__fadeOutBGM(this.__bgmAudioID, this.__bgmUrl, duration, stop)
    // }

    // private async __fadeOutBGM(audioID: number, bgmUrl: string, duration: number, stop: boolean) {
    //     if (audioID < 0) {
    //         return
    //     }
    //     if (stop) {
    //         if (bgmUrl !== '') {
    //             this.__bgmTimeMap[bgmUrl] = {
    //                 curTime: cc.audioEngine.getCurrentTime(audioID),
    //                 stopTime: Date.now()
    //             }
    //         }
    //     }
    //     this.__stateTypeMap[audioID] = 'fadeOut'
    //     let volume = cc.audioEngine.getVolume(audioID)
    //     let target = 0
    //     let diff = (target - volume) / 25 / duration
    //     while (volume + diff >= target) {
    //         volume += diff
    //         cc.audioEngine.setVolume(audioID, volume)
    //         await ut.wait(0.04)
    //         if (this.__stateTypeMap[audioID] !== 'fadeOut') {
    //             return
    //         }
    //     }
    //     if (stop) {
    //         cc.audioEngine.stop(audioID)
    //         if (this.__bgmAudioID === audioID) {
    //             this.__bgmAudioID = -1
    //         }
    //     } else {
    //         cc.audioEngine.setVolume(audioID, target)
    //     }
    //     delete this.__stateTypeMap[audioID]
    // }

    // public async fadeInBGM(duration: number, ratio: number = 1) {
    //     this.__fadeInBGM(this.__bgmAudioID, duration, ratio)
    // }

    // private async __fadeInBGM(audioID: number, duration: number, ratio: number = 1) {
    //     if (audioID < 0) {
    //         return
    //     }
    //     let type = "fadeIn" + ratio
    //     this.__stateTypeMap[audioID] = type
    //     let volume = cc.audioEngine.getVolume(audioID)
    //     let target = this.bgmVolume * this.__bgmVolumeRatio * ratio
    //     let diff = (target - volume) / 25 / duration
    //     while (volume < target) {
    //         volume += diff
    //         cc.audioEngine.setVolume(audioID, volume)
    //         await ut.wait(0.04)
    //         if (this.__stateTypeMap[audioID] !== type) {
    //             return
    //         }
    //     }
    //     cc.audioEngine.setVolume(audioID, target)
    //     delete this.__stateTypeMap[audioID]
    // }

    // // 播发音效
    // public async playSFX(url: string | cc.AudioClip, opts?: { volume: number, startTime: number, loop: boolean, onComplete: Function, tag: string }) {
    //     if (!url) {
    //         return -1
    //     } else if (this.sfxVolume <= 0 && !opts?.loop) {
    //         return -1
    //     }
    //     const tag = opts?.tag || ''
    //     if (url instanceof cc.AudioClip) {
    //         this.__sfxs_infos.push({ url: url.nativeUrl, tag: tag, id: -1 })
    //         return this.__playSFX(url, url.nativeUrl, opts)
    //     }
    //     let audio = this.__getAudio(url)
    //     if (audio) {
    //         this.__sfxs_infos.push({ url: url, tag: tag, id: -1 })
    //         return this.__playSFX(audio, url, opts)
    //     }
    //     this.__sfxs_infos.push({ url: url, tag: tag, id: -1 })
    //     let asset = await this.__load(url)
    //     if (asset?.audio) {
    //         this.__addAudio(asset.mod, asset.url, asset.audio)
    //         return this.__playSFX(asset.audio, url, opts)
    //     }
    //     cc.error('playSFX error url=', url)
    //     return -1
    // }

    // private __playSFX(audio: cc.AudioClip, url: string, opts?: { volume: number, startTime: number, loop: boolean, onComplete: Function, tag: string }) {
    //     const tag = opts?.tag || ''
    //     const info = this.__sfxs_infos.find(m => m.url === url && m.tag === tag)
    //     if (!info) {
    //         return
    //     }
    //     const loop = !!opts?.loop, volume = opts?.volume ?? 1, startTime = opts?.startTime ?? 0, onComplete = opts?.onComplete
    //     const audioId = info.id = cc.audioEngine.play(audio, loop, this.sfxVolume * volume)
    //     if (!loop) { //只有不是循环的才会有回调
    //         cc.audioEngine.setFinishCallback(audioId, () => {
    //             this.__sfxs_infos.remove('id', audioId)
    //             onComplete && onComplete()
    //         })
    //     }
    //     if (startTime) {
    //         cc.audioEngine.setCurrentTime(audioId, startTime)
    //     }
    //     if (this.__isPause) {
    //         cc.audioEngine.pause(audioId)
    //     }
    //     return audioId
    // }

    // public stopSFX(val: number | string | cc.AudioClip, tag: string = '') {
    //     if (!val) {
    //     } else if (typeof (val) === 'number') {
    //         cc.audioEngine.stop(val)
    //         this.__sfxs_infos.remove('id', val)
    //     } else if (typeof (val) === 'string') {
    //         this.__sfxs_infos.delete(m => m.url === val && m.tag === tag).forEach(m => cc.audioEngine.stop(m.id))
    //     } else if (val instanceof cc.AudioClip) {
    //         this.__sfxs_infos.delete(m => m.url === val.url && m.tag === tag).forEach(m => cc.audioEngine.stop(m.id))
    //     }
    // }

    // public fadeOutSFX(duration: number, val: number | string | cc.AudioClip, tag: string = '') {
    //     if (!val) {
    //     } else if (typeof (val) === 'number') {
    //         this.__fadeOutSFX(val, duration)
    //         this.__sfxs_infos.remove('id', val)
    //     } else if (typeof (val) === 'string') {
    //         this.__sfxs_infos.delete(m => m.url === val && m.tag === tag).forEach(m => {
    //             this.__fadeOutSFX(m.id, duration)
    //         })
    //     } else if (val instanceof cc.AudioClip) {
    //         this.__sfxs_infos.delete(m => m.url === val.url && m.tag === tag).forEach(m => {
    //             this.__fadeOutSFX(m.id, duration)
    //         })
    //     }
    // }

    // private async __fadeOutSFX(audioID: number, duration: number) {
    //     if (audioID < 0) {
    //         return
    //     }
    //     let volume = cc.audioEngine.getVolume(audioID)
    //     let target = 0
    //     let diff = (target - volume) / 25 / duration
    //     while (volume + diff >= target) {
    //         volume += diff
    //         cc.audioEngine.setVolume(audioID, volume)
    //         await ut.wait(0.04)
    //     }
    //     cc.audioEngine.stop(audioID)
    // }
}

// window['audioMgr'] = new AudioMgr();
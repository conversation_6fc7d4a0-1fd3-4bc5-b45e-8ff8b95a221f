import { js, Node, Prefab, view } from "cc"
import BaseNoticeCtrl from "../base/BaseNoticeCtrl"
import { loader } from "../utils/ResLoader"

export default class NoticeCtrlMgr {

    public node: Node = null

    private caches: Map<string, BaseNoticeCtrl> = new Map<string, BaseNoticeCtrl>()

    private async readyNot(pfb: Prefab) {
        if (this.caches.has(pfb.name)) {
            return
        }
        const it: Node = mc.instantiate(pfb, this.node)
        const className = it.name + `Ctrl`
        if (!js.getClassByName(className)) {
            return logger.error('loadNotice error! not found class ' + className)
        }
        const notice = it.getComponent(className) || it.addComponent(className)
        if (!notice || !(notice instanceof BaseNoticeCtrl)) {
            return logger.error('loadNotice error! not found class ' + className)
        }
        notice.hide()
        await notice.__create()
        this.caches.set(pfb.name, notice)
    }

    // 加载所有not
    public async loadAll(root: string, complete?: Function, progress?: (done: number, total: number) => void) {
        const debug = loader.debug
        loader.debug = false
        const assets = await loader.loadResDir(root, Prefab, progress)
        loader.debug = debug
        await Promise.all(assets.map(pfb => this.readyNot(pfb)))
        complete && complete()
    }

    // 加载指定not
    public async loadNot(root: string, val: string, complete?: Function) {
        await Promise.all(val.split('|').map(async (name) => {
            if (!name) {
                return
            }
            const pfb = await loader.loadRes(root + '/' + name, Prefab)
            return this.readyNot(pfb)
        }))
        complete && complete()
    }
}
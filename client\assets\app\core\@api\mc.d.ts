type AppParam = {
    debug?: boolean;
    lang?: string;
    changeLang?: boolean;
    pnlIndexConf?: any;
}

type PnlParam = {
    isClean?: boolean;
    isAct?: boolean;
    isMask?: boolean;
    zIndex?: number;
    adaptHeight?: number;
    adIndex?: number;
}

type WindParam = {
    isClean?: boolean;
}

declare namespace mc {

    // @ts-ignore
    import { Node, Component, Prefab, SpriteFrame, Color } from "cc";

    export const GameNameSpace: string;
    export const app: Component;
    export const DEBUG: boolean;
    export const currWind: BaseWindCtrl;
    export const currWindName: string;
    export var lang: string; //当前语言
    export var canChangeLang: boolean;

    export enum Event {
        MVC_LOGGER_PRINT,
        MVC_ERROR_MSG,
        /** (key: string | BasePnlCtrl, ...params: any) */
        OPEN_PNL,
        /** (key: string | BasePnlCtrl) */
        HIDE_PNL,
        /** (val?: string, ignores: string) */
        HIDE_ALL_PNL,
        /** (key: string | BasePnlCtrl, force?: boolean) */
        CLOSE_PNL,
        /** (val?: string, force?: boolean) */
        CLOSE_ALL_PNL,
        /** (mod: string) */
        CLOSE_MOD_PNL,
        /** (key: string, complete?: Function, progress?: (done: number, total: number) => void) */
        PRELOAD_PNL,
        LOAD_BEGIN_PNL,
        LOAD_END_PNL,
        PNL_ENTER,
        PNL_LEAVE,
        PNL_ENTER_PLAY_DONE,
        CLEAN_ALL_UNUSED,
        /** (id: number) */
        GIVEUP_LOAD_PNL,
        /** (isUnlock?: boolean) */
        CLEAN_LOAD_PNL_QUEUE,
        /** (key: string, complete?: Function, ...params: any) */
        GOTO_WIND,
        /** (key: string, complete?: Function, progress?: (done: number, total: number) => void) */
        PRELOAD_WIND,
        WIND_ENTER,
        CLEAN_CACHE_WIND,
        LOAD_BEGIN_WIND,
        LOAD_END_WIND,
        READY_BEGIN_WIND,
        READY_END_WIND,
        /** (val: string, complete?: Function) */
        LOAD_NOTICE,
        /** (complete?: Function, progress?: (done: number, total: number) => void) */
        LOAD_ALL_NOTICE,
        LOADING_WAIT_BEGIN,
        LOADING_WAIT_END,
        LANGUAGE_CHANGED,
    }

    export class BaseViewCtrl extends Component {
        readonly _state: string;
        addListener(type: string, cb: Function, target?: any): void;
        removeListener(type: string): void;
        emit(type: string | number, ...params: any): void;
        addClickEvent(cmpt: Component, handler: string, data?: string): void;
        listenEventMaps(): { enter?: boolean }[];
        getModel<T>(key: string): T;
    }

    export class BasePnlCtrl extends BaseViewCtrl {
        readonly key: string; //传入名
        readonly mod: string; //所属模块名
        readonly url: string; //路径
        readonly zIndex: number; //层级
        onCreate(): Promise<void>;
        onEnter(...params: any): void;
        onRemove(): void;
        onClean(): void;
        hide(): void;
        close(): void;
        setOpacity(val: number): void; //设置UI的透明度
        setParam(opts: PnlParam): void;
    }

    export class BaseWindCtrl extends BaseViewCtrl {
        readonly key: string; //传入名 即模块名
        onCreate(): Promise<void>;
        onEnter(...params: any): void;
        onLeave(): void;
        onClean(): void;
        setParam(opts: WindParam): void;
    }

    export class BaseNoticeCtrl extends BaseViewCtrl {
        onCreate(): Promise<void>;
        onClean(): void;
        open(): void;
        hide(): void;
    }

    export class BaseWdtCtrl extends BaseViewCtrl {
        onCreate(): void;
        onClean(): void;
    }

    export class BaseModel {
        static ins<T>(): T;
        readonly type: string;
        constructor(type: string);
        protected onCreate(): void;
        protected onClean(): void;
        protected emit(type: string | number, ...params: any): void;
        protected getModel<T>(key: string): T;
    }

    // --------------------------------cc扩展---------------------------------------
    // 按钮扩展
    export class ButtonEx extends Component {
        interactable: boolean;
        static DefaultClickPath: string;
    }

    // scrollview扩展
    export class ScrollViewEx extends Component {
        list(len: number, cb?: Function, target?: any): void;
    }

    // scrollview扩展
    export class ScrollViewPlus extends Component {
        isFrameRender: boolean;
        items<T>(list: T[] | number, setItemData?: (it: Node, data: T, i: number) => void, target?: any): void;
        updateNodeShow(): void;
    }

    // 播放等待的点
    export class LabelWaitDot extends Component {
        play(val?: string): void;
        stop(val?: string): void;
    }

    // 滚动数字
    export class LabelRollNumber extends Component {
        string: string;
        setPrefix(val: string): LabelRollNumber;
        set(val: number): LabelRollNumber;
        to(end: number, duration?: number): void;
        by(val: number, duration?: number): void;
        run(val: number, opts?: any): void;
    }

    // 时间文本
    export class LabelTimer extends Component {
        string: string;
        Color(val: string | Color): LabelTimer;
        setPrefix(val: string): LabelTimer;
        setFormat(val: string | ((time: number) => string)): LabelTimer;
        setEndTime(val: number): LabelTimer;
        setPause(val: boolean): void;
        run(time: number, callback?: Function): void;
        getTime(): number;
    }

    // 多选颜色
    export class MultiColor extends Component {
        setColor(idx: number | boolean): void;
    }

    // 多选精灵
    export class MultiFrame extends Component {
        addFrame(sf: SpriteFrame): void;
        setFrame(idx: number | boolean): void;
        getFrame(idx: number | boolean): SpriteFrame;
        getIndex(): number;
        frameCount(): number;
        getSpriteFrame(): SpriteFrame;
        random(): number;
        clean(): void;
    }

    // 多语言label
    export class LocaleFont extends Component {
        string: string;
        setFont(fontUrl: string): void;
        updateLang(): void;
    }

    // 多语言label
    export class LocaleLabel extends Component {
        string: string;
        setKey(key: string, ...params: any[]): void;
        setFont(fontUrl: string): void;
        updateLang(): void;
        updateString(): void;
    }

    // 多语言RichText
    export class LocaleRichText extends Component {
        string: string;
        setKey(key: string, ...params: any[]): void;
        setFont(fontUrl: string): void;
        updateLang(): void;
        updateString(): void;
    }

    // 多语言Sprite
    export class LocaleSprite extends Component {
        setKey(key: string): void;
        addSpriteFrame(val: SpriteFrame): void;
    }

    export function init(name: string, app: Component, opts?: AppParam): void;
    export function lockTouch(key: string): string;
    export function unlockTouch(key: string): string;
    export function isLockTouch(): boolean;
    export function getWindNode(): Node;
    export function getViewNode(): Node;
    export function getNoticeNode(): Node;
    export function getOpenPnls(): BasePnlCtrl[];
    export function instantiate(item: Node | Prefab, parent: Node | Component): Node;

    export function getWindNode(): Node;
    export function getViewNode(): Node;
    export function getNoticeNode(): Node;

    // 添加模块装饰器
    export function addmodel(type: string): Function;
    export function getModel<T>(key: string): T;
    export function pushModel(...params: BaseModel[]): void;
    export function resetModel(model: BaseModel): void;
}
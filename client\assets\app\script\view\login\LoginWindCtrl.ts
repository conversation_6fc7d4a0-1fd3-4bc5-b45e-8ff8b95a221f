import { _decorator, log, EventTouch, Node, Label, JsonAsset, Font, Prefab } from "cc";
import { viewHelper } from "../../common/helper/ViewHelper";
import ca from "db://assets/scene/ca";
import LoginModel from "../../model/login/LoginModel";
import { LoginState } from "../../common/constant/Enums";
import { ecode } from "../../common/constant/ECode";
import { loadProgressHelper } from "../../common/helper/LoadProgressHelper";
const { ccclass } = _decorator;

@ccclass
export default class LoginWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    private versionLbl_: Label = null // path://version_l
    private infoNode_: Node = null // path://info_n
    //@end

    private readonly SCENE_KEY = 'lobby'

    private loadDescText: Label = null
    private loadDescProgress: Label = null

    private model: LoginModel = null

    private curPercent: number = 0
    private dstPercent: number = 0
    private waitLoginButtonBackFunc: Function = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.loadDescText = this.infoNode_.Child('text/val', Label)
        this.loadDescProgress = this.infoNode_.Child('text/progress/val', Label)
        this.model = this.getModel('login')
    }

    public onEnter(data: any) {
        this.versionLbl_.string = 'v.' + ca.VERSION
        this.openLoadDesc('text')
        this.ready()
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://info_n/login_button/guest_login_be
    onClickGuestLogin(event: EventTouch, data: string) {
        this.model.guestLogin().then(data => this.buttonLoginRet(data))
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private openLoadDesc(val: string) {
        this.infoNode_.Data = val
        this.infoNode_.Swih(val)
    }

    private changeLoadDesc(text: string, percent: number = -1) {
        if (this.infoNode_.Data !== 'text') {
            this.openLoadDesc('text')
        }
        this.loadDescText.setLocaleKey(text)
        this.infoNode_.Swih(percent >= 0 ? 'progress' : 'loading', false)
        if (percent >= 0) {
            this.setLoadDescPercent(percent)
        }
    }

    private setLoadDescPercent(percent: number) {
        this.loadDescProgress.string = percent + '%'
    }

    // 准备
    private async ready() {
        if (!this.isValid) {
            return
        } else if (!this.model.isInitBaseAsset()) {
            this.infoNode_.Child('text').Swih('loading')
            // 加载一些必要的资源
            await Promise.all([
                assetsMgr.loadCommonRes('login', JsonAsset),
                assetsMgr.loadCommonRes('f_b_login', Font),
                assetsMgr.loadCommonRes('PNL_MASK', Prefab),
            ])
            await this.loadNotice('NetWaitNot|MessageBoxNot')
            this.model.setInitBaseAsset(true)
        }
        if (!this.isValid) {
            return viewHelper.showMessageBox('login.net_close', {
                ok: () => viewHelper.gotoWind('login'),
                okText: 'login.button_reconnect',
                lockClose: true,
            })
        }
        // 连接服务器
        this.connect()
    }

    // 连接服务器
    private async connect() {
        this.infoNode_.active = true
        this.infoNode_.Swih('')
        this.changeLoadDesc('login.login_game')
        const model = this.model
        let ok = false, cnt = 0, max = 5
        while (true) {
            ok = await model.connect()
            if (ok) {
                break
            } else if (cnt >= max) {
                cnt = 0
                max += 2
                const _ok = await viewHelper.showConnectFail()
                if (!_ok) {
                    break
                }
            } else {
                cnt += 1
                await ut.wait(2)
            }
        }
        if (this.isValid && ok) {
            this.login()
        }
    }

    // 登录
    private async login() {
        const data = await this.tryLogin()
        if (!this.isValid) {
            return
        } else if (data.state === LoginState.FAILURE) {
            if (data.err === ecode.ROOM_CLOSE) {
                viewHelper.showMessageBox('login.net_close', {
                    lockClose: true,
                    okText: 'login.button_reconnect',
                    ok: () => this.ready(),
                })
            }
            return this.openLoadDesc('')
        } else if (data.state === LoginState.VERSION_TOOLOW) { //版本过低
            // this.showVersionLowTip(data.data)
            return this.openLoadDesc('')
        } else if (data.state === LoginState.QUEUE_UP) { //需要排队
            // this.showPnl('login/LineupTip')
            // this.model.disconnect()
            // await eventCenter.wait(EventType.QUEUE_UP_DONE)
            // if (!this.isValid) {
            //     return
            // }
            // return this.connect()
        }
        // 封禁
        if (data.state === LoginState.BANACCOUNT_TIME) {
            // return this.showPnl('login/BanAccountTimeTip', data.time, data.type)
        }
        // 进入大厅
        this.loadLobby()
    }

    // 加载大厅
    private loadLobby() {
        if (!this.isValid) {
            return
        }
        const loadProgress = this.loadAssets()
        loadProgress.add(3, async (cb) => viewHelper.preloadWind(this.SCENE_KEY, cb))
        loadProgress.run(p => this.dstPercent = p)
    }

    // 尝试登陆
    private async tryLogin(accountToken?: string) {
        let data: any = await this.model.tryLogin(accountToken)
        if (data.state === LoginState.NOT_ACCOUNT_TOKEN) {
            // 已经用按钮登陆过了
            if (accountToken) {
                return { state: LoginState.FAILURE }
            }
            // 等待登陆完成
            data = await this.showLoginButton()
            this.changeLoadDesc('login.login_game')
            // 再次尝试登陆
            return this.tryLogin(data.accountToken)
        }
        return data
    }

    // 显示版本过低提示
    private showVersionLowTip(data: any) {
        if (ut.isMobile()) {
            return viewHelper.showPnl('login/AppUpdateTip', data)
        }
        return viewHelper.showPnl('login/VersionLowTip')
    }

    // 显示登陆按钮
    private async showLoginButton() {
        this.openLoadDesc('buttons')
        return new Promise(resolve => this.waitLoginButtonBackFunc = resolve)
    }

    // 登陆结束处理
    private buttonLoginRet(data: any) {
        if (data.state === LoginState.FAILURE) {
            return viewHelper.showMessageBox(data.err)
        } else if (this.waitLoginButtonBackFunc) {
            this.waitLoginButtonBackFunc(data)
        }
    }

    // 加载资源
    private loadAssets() {
        this.changeLoadDesc('login.load_assets', 0)
        // audioMgr.init() //音效
        mc.ButtonEx.DefaultClickPath = 'click' //设置默认点击音效
        this.curPercent = this.dstPercent = 0
        const loadProgress = loadProgressHelper.create()
        if (!this.model.isInitGameAsset()) {
            loadProgress.add(6, async (cb) => assetsMgr.init(cb))
                .add(5, async (cb) => this.loadAllNotice(cb))
        }
        return loadProgress
    }

    private async loadNotice(val: string) {
        return new Promise<void>(resolve => this.emit(mc.Event.LOAD_NOTICE, val, resolve))
    }

    private async loadAllNotice(progessCallback: (percent: number) => void) {
        return new Promise<void>(resolve => this.emit(mc.Event.LOAD_ALL_NOTICE, resolve, (done: number, total: number) => progessCallback(done / total)))
    }

    // 进入游戏
    private async go() {
        this.model.setInitGameAsset(true)
        await ut.wait(1)
        viewHelper.gotoWind(this.SCENE_KEY)
    }

    update(dt: number) {
        if (this.curPercent >= this.dstPercent || !this.loadDescProgress) {
            return
        }
        this.curPercent = Math.min(this.curPercent + 1 * dt, this.dstPercent)
        if (this.curPercent >= 1) {
            this.go()
        } else {
            this.loadDescProgress.string = Math.min(Math.floor(this.curPercent * 100), 99) + ''
        }
    }
}

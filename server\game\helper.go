package game

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
	"casrv/utils/array"
)

// 拷贝属性
func CloneAttrs(attrs [][]int32) [][]int32 {
	ret := [][]int32{}
	for i, l := 0, len(attrs); i < l; i++ {
		ret = append(ret, array.Clone(attrs[i]))
	}
	return ret
}

// 拷贝属性到pb
func CloneAttrsToPb(attrs [][]int32) []*pb.Int32ArrayInfo {
	arr := []*pb.Int32ArrayInfo{}
	for _, attr := range attrs {
		attrInfo := &pb.Int32ArrayInfo{}
		for _, val := range attr {
			attrInfo.Arr = append(attrInfo.Arr, val)
		}
		arr = append(arr, attrInfo)
	}
	return arr
}

// 随机3个物品
func RandomItemsTo3(conf []string) []*Item {
	return array.Map(conf, func(m string, _ int) *Item {
		arr := ut.StringToInt32s(m, ",")
		return NewItem(arr[0], int8(arr[1]))
	})
}

// 生成指定层数的地图数据
func GenerateMapData(layers int32) [][]*MapNode {
	maps := [][]*MapNode{}
	// 生成每一层
	for layer := range layers {
		currentLayer := []*MapNode{}
		if layer == 0 { //第一层固定3个节点
			for range 3 {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
		} else {
			// 其他层根据上一层生成
			prevLayer := maps[layer-1]
			nodeCount := ut.RandomInt32(2, 5) // 每层2-5个节点
			for range nodeCount {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
			// 为上一层的节点分配子节点（避免路径交叉）
			assignChildrenWithoutCrossing(prevLayer, currentLayer)
		}
		maps = append(maps, currentLayer)
	}
	return maps
}

// 根据层数获取随机节点类型
func getRandomNodeType(currentLayer int32) int32 {
	// 每第4层遇到玩家
	if currentLayer%3 == 0 {
		return MAP_NODE_TYPE_PLAYER
	}
	// 其他层随机分配类型
	nodeTypes := []int32{
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_BATTLE, // 战斗节点权重更高
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_SHOP,
		MAP_NODE_TYPE_EVENT,
		MAP_NODE_TYPE_TREASURE,
	}
	return nodeTypes[ut.RandomInt32(0, int32(len(nodeTypes))-1)]
}

// 为父节点分配子节点，避免路径交叉
func assignChildrenWithoutCrossing(parentLayer []*MapNode, childLayer []*MapNode) {
	parentCount := len(parentLayer)
	childCount := len(childLayer)

	if parentCount == 0 || childCount == 0 {
		return
	}

	// 使用严格的区域分割算法
	assignChildrenByStrictRegions(parentLayer, childLayer)
}

// 严格的区域分割算法，确保不会有路径交叉
func assignChildrenByStrictRegions(parentLayer []*MapNode, childLayer []*MapNode) {
	parentCount := len(parentLayer)
	childCount := len(childLayer)

	// 为每个父节点分配严格的子节点区域
	for i, parent := range parentLayer {
		// 计算当前父节点的严格区域
		startIndex, endIndex := calculateStrictRegion(i, parentCount, childCount)

		// 在区域内随机选择1-3个子节点
		regionSize := endIndex - startIndex + 1
		maxConnections := ut.MinInt32(3, int32(regionSize))
		if maxConnections < 1 {
			maxConnections = 1
		}
		connectionCount := ut.RandomInt32(1, maxConnections)

		// 在区域内随机选择子节点
		availableIndices := make([]int32, 0)
		for j := startIndex; j <= endIndex; j++ {
			availableIndices = append(availableIndices, int32(j))
		}

		// 随机打乱
		for k := len(availableIndices) - 1; k > 0; k-- {
			j := ut.RandomInt32(0, int32(k))
			availableIndices[k], availableIndices[j] = availableIndices[j], availableIndices[k]
		}

		// 选择子节点
		for j := int32(0); j < connectionCount && j < int32(len(availableIndices)); j++ {
			childIndex := availableIndices[j]
			parent.Children = append(parent.Children, childIndex)
		}
	}
}

// 计算父节点的严格区域，确保不会交叉
func calculateStrictRegion(parentIndex, parentCount, childCount int) (int, int) {
	if parentCount == 1 {
		// 如果只有一个父节点，可以连接所有子节点
		return 0, childCount - 1
	}

	// 将子节点平均分配给父节点，不允许重叠
	baseSize := childCount / parentCount
	remainder := childCount % parentCount

	// 计算当前父节点的起始位置
	startIndex := parentIndex * baseSize
	if parentIndex < remainder {
		startIndex += parentIndex
	} else {
		startIndex += remainder
	}

	// 计算当前父节点的区域大小
	regionSize := baseSize
	if parentIndex < remainder {
		regionSize++
	}

	endIndex := startIndex + regionSize - 1

	// 确保索引在有效范围内
	if startIndex >= childCount {
		startIndex = childCount - 1
	}
	if endIndex >= childCount {
		endIndex = childCount - 1
	}
	if startIndex > endIndex {
		endIndex = startIndex
	}

	return startIndex, endIndex
}

// 计算父节点可以连接的子节点范围
func calculateChildRange(parentIndex, parentCount, childCount int) (int, int) {
	if parentCount == 1 {
		// 如果只有一个父节点，可以连接所有子节点
		return 0, childCount - 1
	}

	// 将子节点分成若干区域，每个父节点对应一个区域
	// 允许相邻区域有一定重叠，避免路径交叉

	// 计算每个父节点的基础范围
	baseRangeSize := float64(childCount) / float64(parentCount)

	// 计算当前父节点的中心位置
	centerPos := float64(parentIndex)*baseRangeSize + baseRangeSize/2

	// 计算范围的一半大小（允许一定的重叠）
	halfRange := baseRangeSize * 0.7 // 70%的基础范围，允许30%的重叠

	// 计算最小和最大索引
	minIndex := int(centerPos - halfRange)
	maxIndex := int(centerPos + halfRange)

	// 确保索引在有效范围内
	if minIndex < 0 {
		minIndex = 0
	}
	if maxIndex >= childCount {
		maxIndex = childCount - 1
	}

	// 确保至少有一个可连接的子节点
	if minIndex > maxIndex {
		if parentIndex < parentCount/2 {
			minIndex = 0
			maxIndex = 0
		} else {
			minIndex = childCount - 1
			maxIndex = childCount - 1
		}
	}

	return minIndex, maxIndex
}

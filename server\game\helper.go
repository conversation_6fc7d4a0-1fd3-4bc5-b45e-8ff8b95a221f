package game

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
	"casrv/utils/array"
	"slices"
)

// 拷贝属性
func CloneAttrs(attrs [][]int32) [][]int32 {
	ret := [][]int32{}
	for i, l := 0, len(attrs); i < l; i++ {
		ret = append(ret, array.Clone(attrs[i]))
	}
	return ret
}

// 拷贝属性到pb
func CloneAttrsToPb(attrs [][]int32) []*pb.Int32ArrayInfo {
	arr := []*pb.Int32ArrayInfo{}
	for _, attr := range attrs {
		attrInfo := &pb.Int32ArrayInfo{}
		for _, val := range attr {
			attrInfo.Arr = append(attrInfo.Arr, val)
		}
		arr = append(arr, attrInfo)
	}
	return arr
}

// 随机3个物品
func RandomItemsTo3(conf []string) []*Item {
	return array.Map(conf, func(m string, _ int) *Item {
		arr := ut.StringToInt32s(m, ",")
		return NewItem(arr[0], int8(arr[1]))
	})
}

// 生成指定层数的地图数据
func GenerateMapData(layers int32) [][]*MapNode {
	maps := [][]*MapNode{}
	// 生成每一层
	for layer := range layers {
		currentLayer := []*MapNode{}
		if layer == 0 { //第一层固定3个节点
			for range 3 {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
		} else {
			// 其他层根据上一层生成
			prevLayer := maps[layer-1]
			nodeCount := ut.RandomInt32(2, 5) // 每层2-5个节点
			for range nodeCount {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
			// 为上一层的节点分配子节点（避免路径交叉）
			assignChildrenByStrictRegions(prevLayer, currentLayer)
		}
		maps = append(maps, currentLayer)
	}
	return maps
}

// 根据层数获取随机节点类型
func getRandomNodeType(currentLayer int32) int32 {
	// 每第4层遇到玩家
	if currentLayer%3 == 0 {
		return MAP_NODE_TYPE_PLAYER
	}
	// 其他层随机分配类型
	nodeTypes := []int32{
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_BATTLE, // 战斗节点权重更高
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_SHOP,
		MAP_NODE_TYPE_EVENT,
		MAP_NODE_TYPE_TREASURE,
	}
	return nodeTypes[ut.RandomInt32(0, int32(len(nodeTypes))-1)]
}

// 严格的区域分割算法，确保不会有路径交叉且每个子节点都有父节点
func assignChildrenByStrictRegions(parentLayer []*MapNode, childLayer []*MapNode) {
	parentCount := len(parentLayer)
	childCount := len(childLayer)
	if parentCount == 0 || childCount == 0 {
		return
	}
	// 第一步：确保每个子节点都有至少一个父节点
	childHasParent := make([]bool, childCount)
	// 为每个父节点分配严格的子节点区域
	for i, parent := range parentLayer {
		// 计算当前父节点的严格区域
		startIndex, endIndex := calculateStrictRegion(i, parentCount, childCount)
		// 确保区域内至少有一个子节点被连接
		if startIndex <= endIndex {
			// 随机选择区域内的一个子节点作为必连节点
			guaranteedChild := ut.RandomInt32(int32(startIndex), int32(endIndex))
			parent.Children = append(parent.Children, guaranteedChild)
			childHasParent[guaranteedChild] = true
		}
	}
	// 第二步：检查是否有子节点没有父节点，如果有则分配给最近的父节点
	for childIndex, hasParent := range childHasParent {
		if !hasParent {
			// 找到最适合的父节点（区域包含此子节点的父节点）
			bestParent := findBestParentForChild(childIndex, parentCount, childCount)
			if bestParent >= 0 && bestParent < len(parentLayer) {
				parentLayer[bestParent].Children = append(parentLayer[bestParent].Children, int32(childIndex))
				childHasParent[childIndex] = true
			}
		}
	}
	// 第三步：为父节点添加额外的子节点（在不违反无交叉原则的前提下）
	for i, parent := range parentLayer {
		startIndex, endIndex := calculateStrictRegion(i, parentCount, childCount)
		// 计算当前父节点可以添加的额外子节点
		maxAdditionalConnections := ut.MinInt32(2, int32(endIndex-startIndex)) // 最多再添加2个
		if maxAdditionalConnections > 0 {
			additionalCount := ut.RandomInt32(0, maxAdditionalConnections)
			// 在区域内寻找还未连接的子节点
			availableChildren := make([]int32, 0)
			for j := startIndex; j <= endIndex; j++ {
				// 检查这个子节点是否已经被当前父节点连接
				if childIndex := int32(j); !slices.Contains(parent.Children, childIndex) {
					availableChildren = append(availableChildren, childIndex)
				}
			}
			// 随机选择额外的子节点
			for k := int32(0); k < additionalCount && k < int32(len(availableChildren)); k++ {
				randomIndex := ut.RandomInt32(0, int32(len(availableChildren))-1)
				selectedChild := availableChildren[randomIndex]
				parent.Children = append(parent.Children, selectedChild)
				// 从可用列表中移除已选择的子节点
				availableChildren = append(availableChildren[:randomIndex], availableChildren[randomIndex+1:]...)
			}
		}
	}
}

// 计算父节点的严格区域，确保不会交叉
func calculateStrictRegion(parentIndex, parentCount, childCount int) (int, int) {
	if parentCount == 1 {
		// 如果只有一个父节点，可以连接所有子节点
		return 0, childCount - 1
	}
	// 将子节点平均分配给父节点，不允许重叠
	baseSize := childCount / parentCount
	remainder := childCount % parentCount
	// 计算当前父节点的起始位置
	startIndex := parentIndex * baseSize
	if parentIndex < remainder {
		startIndex += parentIndex
	} else {
		startIndex += remainder
	}
	// 计算当前父节点的区域大小
	regionSize := baseSize
	if parentIndex < remainder {
		regionSize++
	}
	endIndex := startIndex + regionSize - 1
	// 确保索引在有效范围内
	if startIndex >= childCount {
		startIndex = childCount - 1
	}
	if endIndex >= childCount {
		endIndex = childCount - 1
	}
	if startIndex > endIndex {
		endIndex = startIndex
	}
	return startIndex, endIndex
}

// 为孤立的子节点找到最适合的父节点
func findBestParentForChild(childIndex, parentCount, childCount int) int {
	// 找到哪个父节点的区域包含这个子节点
	for parentIndex := range parentCount {
		startIndex, endIndex := calculateStrictRegion(parentIndex, parentCount, childCount)
		if childIndex >= startIndex && childIndex <= endIndex {
			return parentIndex
		}
	}
	// 如果没有找到合适的区域（理论上不应该发生），返回最近的父节点
	bestParent := 0
	minDistance := childCount
	for parentIndex := range parentCount {
		startIndex, endIndex := calculateStrictRegion(parentIndex, parentCount, childCount)
		centerPos := (startIndex + endIndex) / 2
		distance := ut.Abs(childIndex - centerPos)
		if distance < minDistance {
			minDistance = distance
			bestParent = parentIndex
		}
	}
	return bestParent
}

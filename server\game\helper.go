package game

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
	"casrv/utils/array"
)

// 拷贝属性
func CloneAttrs(attrs [][]int32) [][]int32 {
	ret := [][]int32{}
	for i, l := 0, len(attrs); i < l; i++ {
		ret = append(ret, array.Clone(attrs[i]))
	}
	return ret
}

// 拷贝属性到pb
func CloneAttrsToPb(attrs [][]int32) []*pb.Int32ArrayInfo {
	arr := []*pb.Int32ArrayInfo{}
	for _, attr := range attrs {
		attrInfo := &pb.Int32ArrayInfo{}
		for _, val := range attr {
			attrInfo.Arr = append(attrInfo.Arr, val)
		}
		arr = append(arr, attrInfo)
	}
	return arr
}

// 随机3个物品
func RandomItemsTo3(conf []string) []*Item {
	return array.Map(conf, func(m string, _ int) *Item {
		arr := ut.StringToInt32s(m, ",")
		return NewItem(arr[0], int8(arr[1]))
	})
}

// 生成指定层数的地图数据
func GenerateMapData(layers int32) [][]*MapNode {
	maps := [][]*MapNode{}
	// 生成每一层
	for layer := range layers {
		currentLayer := []*MapNode{}
		if layer == 0 { //第一层固定3个节点
			for range 3 {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
		} else {
			// 其他层根据上一层生成
			prevLayer := maps[layer-1]
			nodeCount := ut.RandomInt32(2, 5) // 每层2-5个节点
			for range nodeCount {
				node := &MapNode{
					Type:     getRandomNodeType(layer),
					Children: []int32{},
				}
				currentLayer = append(currentLayer, node)
			}
			// 为上一层的节点分配子节点
			for _, prevNode := range prevLayer {
				// 每个节点至少有1个子节点，最多有3个
				childCount := ut.RandomInt32(1, 3)
				childCount = ut.MinInt32(childCount, int32(len(currentLayer)))
				// 随机选择子节点
				selectedChildren := make(map[int32]bool)
				for range childCount {
					for {
						childIndex := ut.RandomInt32(0, int32(len(currentLayer))-1)
						if !selectedChildren[childIndex] {
							selectedChildren[childIndex] = true
							prevNode.Children = append(prevNode.Children, childIndex)
							break
						}
					}
				}
			}
		}
		maps = append(maps, currentLayer)
	}
	return maps
}

// 根据层数获取随机节点类型
func getRandomNodeType(currentLayer int32) int32 {
	// 每第4层遇到玩家
	if currentLayer%3 == 0 {
		return MAP_NODE_TYPE_PLAYER
	}
	// 其他层随机分配类型
	nodeTypes := []int32{
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_BATTLE, // 战斗节点权重更高
		MAP_NODE_TYPE_BATTLE,
		MAP_NODE_TYPE_SHOP,
		MAP_NODE_TYPE_EVENT,
		MAP_NODE_TYPE_TREASURE,
	}
	return nodeTypes[ut.RandomInt32(0, int32(len(nodeTypes))-1)]
}

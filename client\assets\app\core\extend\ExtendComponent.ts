
/**
 * Component扩展方法
 */

import { Color, Component, Vec3 } from "cc"

if (!Component.prototype.hasOwnProperty('transform')) {
    Object.defineProperty(Component.prototype, 'transform', {
        get() {
            return this.node.transform
        }
    })
}

Component.prototype.getActive = function () {
    return this.node.active
}

Component.prototype.setActive = function (val: boolean) {
    this.node.active = val
    return val
}

Component.prototype.getPosition = function (out?: Vec3) {
    return this.node.getPosition(out)
}

Component.prototype.FindChild = function (name: string | number, className?: any): any {
    return this.node.FindChild(name, className)
}

Component.prototype.Child = function (name: string | number, className?: any): any {
    return this.node.Child(name, className)
}

Component.prototype.Component = function (className: any): any {
    return this.node.Component(className)
}

Component.prototype.Color = function (val: string | Color) {
    this.node.Color(val)
    return this
}

Component.prototype.getColor = function () {
    return this.node.color
}
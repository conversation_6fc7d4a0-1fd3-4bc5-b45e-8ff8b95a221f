import RandomObj from "../common/RandomObj"
import GameModel from "../game/GameModel"
import FSPFighter from "./FSPFighter"

// 战斗逻辑 演绎战斗逻辑

// 回合开始时：
// 攻击前：相邻的肉食动物+❤生命1，+🐟速度1
// 攻击时：相邻动物攻击1次
// 攻击后：+❤1，+🗡1
//      受击时：+反击率20%
//      受击后：+❤1，+🗡1
//      击杀目标后：永久+🗡1，+❤1
// 回合结束时：

// 受击时：+反击率20%
// 受击后：+❤1，+🗡1
// 击杀目标后：永久+🗡1，+❤1
// 击杀目标后：+🗡1，+❤1
// 反击时：其他1个动物+🐓追击1
// 暴击时：相邻动物攻击1次

// 暴击率：10%
// 暴击伤害+100%
// 追击：1
// 追击：每回合可额外行动的次数
// 反击率：在受到敌方攻击时，有几率对攻击者发起1次攻击，不触发追击效果
export default class FSPController {

    private game: GameModel = null

    private random: RandomObj = null
    private fighters: FSPFighter[] = [] //战斗者列表
    private currentFighter: FSPFighter = null //当前回合出战者

    constructor() {
        this.game = GameModel.ins()
    }

    public init(data: any) {
        this.fighters = []
        data.fighters?.forEach(m => {
            const list = m.camp === 1 ? this.game.getBattleAnimals() : this.game.getEnemyAnimals()
            const animal = list.find(x => x.uid === m.uid)
            const fighter = new FSPFighter().init(animal, m, this)
            this.fighters.push(fighter)
        })
        // 根据出手顺序排序
        this.fighters.sort((a, b) => a.attackIndex - b.attackIndex)
        // 初始化随机种子
        this.random = new RandomObj(data.randSeed)
        this.currentFighter = this.fighters[0]
        return this
    }

    public stop() {

    }

    public getCurrentFrameIndex() { return this.game.getFspModel()?.getCurrentFrameIndex() ?? -1 }
    public getRandom() { return this.random }

    // 刷新一帧
    public updateFrame(dt: number) {
        if (!this.currentFighter || this.fighters.length === 0) {
            return
        } else if (this.currentFighter.isRoundEnd()) {
            // 删除死亡的士兵
            const uid = this.removeDieFighters()
            // 检测战斗是否结束
            if (this.checkBattleOver()) {
                return this.game.battleEndByLocal()
            }
            // 上一个结束
            this.currentFighter.endAction()
            // 下一个出手
            this.currentFighter = this.getNextFighter()
            this.currentFighter.beginAction()
            this.currentFighter.updateBuff()
            if (uid) {
                this.fighters.delete(m => m.getUid() === uid)
            }
        }
        this.currentFighter.behaviorTick(dt) //执行士兵的逻辑
    }

    // 获取下一个战斗者
    private getNextFighter() {
        const uid = this.currentFighter.getUid()
        let i = 0, cnt = this.fighters.length
        for (i = 0; i < cnt; i++) {
            if (this.fighters[i].getUid() === uid) {
                break
            }
        }
        i = ut.loopValue(i + 1, cnt)
        return this.fighters[i]
    }

    private checkBattleOver() {
        if (this.fighters.length === 0) {
            return true //没有士兵肯定就结束了啊
        }
        const campMap = {}
        this.fighters.forEach(m => {
            if (!m.isDie()) {
                campMap[m.getCamp()] = true
            }
        })
        // log("checkBattleOver campMap", campMap)
        // 只要还只剩下一个阵营 就算结束
        return Object.keys(campMap).length <= 1
    }

    // 刷新士兵死亡情况
    public removeDieFighters(): string {
        let removeCurrUid = ''
        for (let i = this.fighters.length - 1; i >= 0; i--) {
            const f = this.fighters[i]
            if (f.isDie()) {
                if (f.getUid() === this.currentFighter?.getUid()) {
                    removeCurrUid = f.getUid() //当前出手的士兵死亡 等到获取下一个出手士兵后再删除
                } else {
                    this.fighters.splice(i, 1)
                }
                // 删除实际军队里面的 这里不发送事件到视图层 因为要做死亡动画
                // this.game.removeAnimalByBattle(f.camp, f.getUid())
            }
        }
        return removeCurrUid
    }

    public getAttackTarget(camp: number) {
        return this.fighters.filter(m => m.getCamp() !== camp && !m.isDie()).sort((a, b) => a.getIndex() - b.getIndex())[0]
    }

    public getAttackDamage(attacker: FSPFighter, defender: FSPFighter) {
        return { damage: attacker.getAttack(), isCrit: false }
    }
}
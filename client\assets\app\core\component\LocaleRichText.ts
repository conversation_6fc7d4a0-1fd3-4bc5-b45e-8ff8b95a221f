import { _decorator, CCString, RichText } from "cc";
import BaseLocale from "../base/BaseLocale";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property, menu, requireComponent } = _decorator;

@ccclass
@menu('多语言组件/LocaleRichText')
@requireComponent(RichText)
export default class LocaleRichText extends BaseLocale {

    @property(CCString)
    private key: string = ''
    @property(CCString)
    private fontName: string = ''

    private _label: RichText = null

    private _string: string = ''
    private _lang: string = ''
    private _font: string = ''
    private _json: any = null

    private _params: any[] = []
    private _temp_params: any[] = [] //转换好过后的参数
    private _change: boolean = false

    private _is_empty_string: boolean = false //是否主动设置空字符串

    private get label() {
        if (!this._label) {
            this._label = this.getComponent(RichText)
        }
        return this._label
    }

    onEnable() {
        if (!mc.lang) {
            return
        } else if (this._lang !== mc.lang) {
            this._lang = mc.lang
            this._font = ''
            this._json = null
            this.updateTempParams()
        } else if (this.label.font && !this.label.font.isValid) {
            this._font = ''
        }
        if (!this._json) {
            this.updateJson()
        } else if (!this._font) {
            this.updateFont()
        }
        this.updateString()
        this._change = mc.canChangeLang
        if (this._change) {
            eventCenter.on(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    onDisable() {
        if (this._change) {
            this._change = false
            eventCenter.off(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    // 语言切换
    private onLanguageChanged(lang: string) {
        this._lang = lang
        this._font = ''
        this.updateTempParams()
        this.updateJson()
        this.updateString()
    }

    public set string(val: string) {
        this.label.string = val
        this._is_empty_string = val === ''
    }
    public get string() { return this.label.string }

    public updateLang() {
        this._lang = mc.lang
    }

    // 刷新string
    public updateString() {
        const val = this._json ? this._json[this._lang] : undefined
        if (val !== undefined) {
            this._string = ut.stringFormat(val, this._temp_params)
        } else if (this.key) {
            this._string = ut.stringFormat(this.key, this._temp_params)
        } else if (this._is_empty_string) {
            this._string = ''
        } else {
            this._string = '404'
        }
        if (this._string !== this.label.string) {
            this.label.string = this._string
        }
    }

    // 设置参数
    public setParams(params: any[]) {
        this._params.length = 0
        params?.forEach(m => Array.isArray(m) ? this._params.pushArr(m) : this._params.push(m))
        this.updateTempParams()
    }

    // 刷新参数
    private updateTempParams() {
        this._temp_params = this._params.map(m => {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                const [name, id] = m.split('.')
                const json = assetsMgr.getJsonData(name, id) || {}
                const val = json[this._lang]
                return val !== undefined ? val : m
            }
            return m
        })
    }

    // 刷新json
    private updateJson() {
        if (this.key) {
            const [name, id] = this.key.split('.')
            this._json = assetsMgr.getJsonData(name, id) || {}
            this.updateFont()
        } else {
            this._json = null
        }
    }

    // 刷新字体
    private updateFont() {
        if (this.fontName) {
            if (this.fontName !== this._font) {
                this.setFont(this.fontName)
            }
        } else if (this._json && this._json.font && this._json.font !== this._font) {
            this.setFont(this._json.font)
        }
    }

    public setFont(fontUrl: string) {
        this._font = fontUrl
        const font: any = assetsMgr.getFont(fontUrl)
        if (font) {
            this.label.string = ''
            this.label.font = font
        } else {
            this.label.font = null
            this._font = ''
        }
    }

    // 设置key
    public setKey(key: string, ...params: any[]) {
        if (!this._lang) {
            this._lang = mc.lang
        }
        if (this.key !== key || !this._json) {
            this.key = key
            this.string = ''
            this.updateJson()
        }
        this._is_empty_string = key === ''
        this.setParams(params)
        this.updateString()
    }
}

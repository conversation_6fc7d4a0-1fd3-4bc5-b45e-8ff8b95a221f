import { _decorator, Component, Node, Vec2, Vec3 } from "cc";
import RoleObj from "../../model/game/RoleObj";
import FrameAnimationCmpt from "../cmpt/FrameAnimationCmpt";
import GameModel from "../../model/game/GameModel";
import { getRoleFrameAnimConf } from "../../common/config/RoleFrameAnimConf";
const { ccclass, property } = _decorator;

// 一个角色
@ccclass
export default class RoleCmpt extends Component {

    private key: string = ''

    public data: RoleObj = null
    private body: Node = null
    private animNode: Node = null
    private animCmpt: FrameAnimationCmpt = null
    private currAnimName: string = ''

    public async init(data: RoleObj, pos: Vec3, key: string) {
        this.data = data
        this.node.setPosition(pos)
        this.body = this.FindChild('body')
        // this.body.scale.set(this.data.isFriendly() ? 1 : -1, 1)
        this.animNode = this.FindChild('body/anim')
        this.animCmpt = this.animNode.getComponent(FrameAnimationCmpt)
        this.animCmpt.setUpdateModel(GameModel.ins())
        await this.animCmpt.init(getRoleFrameAnimConf(data.id), key)
        return this
    }

    // 播放动画
    public playAnimation(name: string, cb?: Function, startTime?: number) {
        this.currAnimName = name
        this.animCmpt?.play(name, cb, startTime)
    }
}
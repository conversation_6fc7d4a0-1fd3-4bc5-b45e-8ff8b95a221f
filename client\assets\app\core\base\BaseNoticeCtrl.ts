import { Layers } from "cc";
import BaseViewCtrl from "./BaseViewCtrl";

export default class BaseNoticeCtrl extends BaseViewCtrl {

    public async __create() {
        this._state = 'create'
        this.node.layer = Layers.Enum.UI_2D
        this.__listenMaps()
        this.__register('create')
        await this.onCreate()
    }

    public open() {
        if (this._state === 'enter') {
            return
        }
        this._state = 'enter'
        this.node.active = true
        this.__register('enter')
    }

    public hide() {
        if (this._state === 'remove') {
            return
        }
        this._state = 'remove'
        this.node.active = false
        this.__unregister('enter')
    }

    public onDestroy() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
    }

    public async onCreate() {
    }

    public onClean() {
    }
}
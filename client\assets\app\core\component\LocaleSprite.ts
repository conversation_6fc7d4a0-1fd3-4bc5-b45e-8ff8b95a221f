import { _decorator, CCString, Sprite, Sprite<PERSON>rame } from "cc";
import BaseLocale from "../base/BaseLocale";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property, menu, requireComponent } = _decorator;

@ccclass
@menu('多语言组件/LocaleSprite')
@requireComponent(Sprite)
export default class LocaleSprite extends BaseLocale {

    @property(CCString)
    private key: string = ''
    @property([SpriteFrame])
    private frames: SpriteFrame[] = []

    private _sprite: Sprite = null!

    private _json: any = null
    private _lang: string = ''
    private _change: boolean = false

    private get sprite() {
        if (!this._sprite) {
            this._sprite = this.getComponent(Sprite)!
        }
        return this._sprite
    }

    onEnable() {
        if (!mc.lang) {
            return
        }
        this._change = mc.canChangeLang
        if (this._change) {
            eventCenter.on(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
        this._lang = mc.lang
        if (!this._json) {
            this.updateJson()
        }
        this.updateSprite()
    }

    onDisable() {
        if (mc.lang && this._change) {
            eventCenter.off(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    // 语言切换
    private onLanguageChanged(lang: string) {
        this._lang = lang
        this.updateSprite()
    }

    // 刷新string
    private async updateSprite() {
        const val = this._json[this._lang]
        if (val !== undefined) {
            let spr: SpriteFrame = this.frames.find(m => m.name === val)!
            if (!spr) {
                spr = await assetsMgr.loadTempRes(val, SpriteFrame, '__lang_sprite')
            }
            if (spr) {
                this.sprite.spriteFrame = spr
            }
        }
    }

    // 刷新json
    private updateJson() {
        const [name, id] = this.key.split('.')
        this._json = assetsMgr.getJsonData(name, id) || {}
    }

    // 设置key
    public setKey(key: string) {
        if (this.key !== key || !this._json) {
            this.key = key
            this.updateJson()
        }
        this.updateSprite()
    }

    public addSpriteFrame(val: SpriteFrame) {
        this.frames.push(val)
    }
}

import { Layers, _decorator } from "cc";
import BaseLayerCtrl from "../base/BaseLayerCtrl";
import CoreEventType from "../event/CoreEventType";
import NoticeCtrlMgr from "../manage/NoticeCtrlMgr";

const { ccclass, property } = _decorator;

@ccclass
export default class NoticeLayerCtrl extends BaseLayerCtrl {

    private ctrlMgr: NoticeCtrlMgr = null!

    public listenEventMaps() {
        return [
            { [CoreEventType.LOAD_ALL_NOTICE]: this.onLoadAllNotice },
            { [CoreEventType.LOAD_NOTICE]: this.onLoadNotice },
        ]
    }

    public onCreate() {
        this.node.layer = Layers.Enum.UI_2D
    }

    public onClean() {

    }

    public setCtrlMgr(mgr: NoticeCtrlMgr) {
        this.ctrlMgr = mgr
        this.ctrlMgr.node = this.node
    }

    private onLoadAllNotice(complete?: Function, progress?: (done: number, total: number) => void) {
        this.ctrlMgr.loadAll('view/notice', complete, progress)
    }

    private onLoadNotice(val: string, complete?: Function) {
        this.ctrlMgr.loadNot('view/notice', val, complete)
    }
}
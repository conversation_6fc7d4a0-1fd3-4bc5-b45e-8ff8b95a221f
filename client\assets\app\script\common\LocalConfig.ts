
/**
 * 本地配置文件（该文件是用来忽略各个开发者本地不一样的配置）
 */

class LocalConfig {

    public readonly RELEASE: boolean = false //是否开发模式

    public openGuide: boolean = true //是否开启新手引导
    public openLog: boolean = false //打印日志
    public openPrint: boolean = false //是否打开打印

    public servers = { //正式服
        china: { //国内
            host: 'nine.twomiles.cn',
            port: 8653,
            useSSL: true
        },
        hk: { //海外-香港
            host: 'nine-hk.twomiles.cn',
            port: 8653,
            useSSL: true
        },
        us: { //海外-美服
            host: 'nine-us.twomiles.cn',
            port: 8653,
            useSSL: true
        }
    }

    public server_test: any = { //测试服
        host: '127.0.0.1', //本地
        port: 8653,
        // host: 'nine-test.twomiles.cn', //新加坡 测试服
        // port: 8653,
        // useSSL: true
        // host: 'nine-hk.twomiles.cn', //香港
        // port: 8653,
        // useSSL: true
        // host: 'nine-us.twomiles.cn', //硅谷
        // port: 8653,
        // useSSL: true
        // host: 'nine.twomiles.cn', //国内
        // port: 8653,
        // useSSL: true,
        // host: 'nine-dxf.twomiles.cn', //测试服
        // port: 8653,
        // useSSL: true,
        // host: 'nine-dxf.twomiles.cn', //提审服
        // port: 8653,
        // useSSL: true
    }

    public httpServerUrl = {
        test: 'https://nine-test.twomiles.cn:8181',
        china: 'https://nine.twomiles.cn:8080',
        hk: 'https://nine-hk.twomiles.cn:8080'
    }

    public config = {
        china: {
            checkUpdateUrl: this.httpServerUrl.china + '/getHotUpdateInfo', //获取热更新信息
            maintainTimeUrl: this.httpServerUrl.china + '/getUpdateMaintainTime', //获取维护时间
            hotUpdateStartUrl: this.httpServerUrl.china + '/hotUpdateStart', //热更新开始
            hotUpdateEndUrl: this.httpServerUrl.china + '/hotUpdateEnd', //热更新结束
            packageUrl: 'https://jwm-inland-file.twomiles.cn/hotUpdate/file/',
            manifestUrl: 'https://jwm-inland-file.twomiles.cn/hotUpdate/',
            downloadUrl: {
                ios: 'https://www.baidu.com/',
                google: 'https://www.baidu.com/',
            },
        },
        hk: {
            checkUpdateUrl: 'https://nine-test.twomiles.cn:8181/getHotUpdateInfo',
            // checkUpdateUrl: this.httpServerUrl.hk + '/getHotUpdateInfo',
            maintainTimeUrl: this.httpServerUrl.hk + '/getUpdateMaintainTime', //获取维护时间
            hotUpdateStartUrl: this.httpServerUrl.hk + '/hotUpdateStart', //热更新开始
            hotUpdateEndUrl: this.httpServerUrl.hk + '/hotUpdateEnd', //热更新结束
            packageUrl: 'https://jwm-global-file.twomiles.cn/hotUpdate/file/',
            manifestUrl: 'https://jwm-global-file.twomiles.cn/hotUpdate/',
            downloadUrl: {
                ios: 'https://apps.apple.com/app/id6446716937',
                google: 'https://play.google.com/store/apps/details?id=twgame.global.acers',
            },
        }
    }

    public errorReportUrl = {
        test: 'http://127.0.0.1:8181/errorReport',
        china: this.httpServerUrl.china + '/errorReport',
        hk: this.httpServerUrl.hk + '/errorReport',
    }

    public getServerInfoUrl = {
        // test: 'https://nine-us.twomiles.cn:8080/getServerInfo',
        // test: 'https://nine-test.twomiles.cn:8181/getServerInfo',
        test: 'http://127.0.0.1:8181/getServerInfo',
        china: this.httpServerUrl.china + '/getServerInfo',
        hk: this.httpServerUrl.hk + '/getServerInfo',
    }

    constructor() {
        this.openGuide = this.RELEASE ? true : this.openGuide
        this.server_test = this.RELEASE ? { host: 'nine-test.twomiles.cn', port: 8653, useSSL: true } : this.server_test
        this.httpServerUrl.test = this.RELEASE ? 'https://nine-test.twomiles.cn:8181' : this.httpServerUrl.test
        this.errorReportUrl.test = this.RELEASE ? 'https://nine-test.twomiles.cn:8181/errorReport' : this.errorReportUrl.test
        this.getServerInfoUrl.test = this.RELEASE ? 'https://nine-test.twomiles.cn:8181/getServerInfo' : this.getServerInfoUrl.test
    }
}

export const localConfig = new LocalConfig()
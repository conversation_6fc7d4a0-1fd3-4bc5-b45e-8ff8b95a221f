package game

import (
	"fmt"
	"testing"
)

// 测试路径交叉检测
func TestNoCrossingPaths(t *testing.T) {
	// 生成多个地图进行测试
	for testCase := 0; testCase < 10; testCase++ {
		fmt.Printf("=== 测试案例 %d ===\n", testCase+1)
		
		// 生成5层地图
		mapData := GenerateMapData(5)
		
		// 检查每一层的路径是否有交叉
		for layerIndex := 0; layerIndex < len(mapData)-1; layerIndex++ {
			currentLayer := mapData[layerIndex]
			nextLayer := mapData[layerIndex+1]
			
			fmt.Printf("第%d层到第%d层:\n", layerIndex+1, layerIndex+2)
			fmt.Printf("  父节点数: %d, 子节点数: %d\n", len(currentLayer), len(nextLayer))
			
			// 打印连接关系
			for i, parent := range currentLayer {
				fmt.Printf("  节点%d -> %v\n", i, parent.Children)
			}
			
			// 检查是否有路径交叉
			crossings := detectPathCrossings(currentLayer, nextLayer)
			if len(crossings) > 0 {
				t.<PERSON><PERSON>rf("第%d层到第%d层发现路径交叉: %v", layerIndex+1, layerIndex+2, crossings)
				for _, crossing := range crossings {
					fmt.Printf("  交叉: 节点%d->%d 与 节点%d->%d\n", 
						crossing.Parent1, crossing.Child1, crossing.Parent2, crossing.Child2)
				}
			} else {
				fmt.Printf("  ✓ 无路径交叉\n")
			}
			fmt.Println()
		}
	}
}

// 路径交叉信息
type PathCrossing struct {
	Parent1, Child1 int
	Parent2, Child2 int
}

// 检测路径交叉
func detectPathCrossings(parentLayer []*MapNode, childLayer []*MapNode) []PathCrossing {
	var crossings []PathCrossing
	
	// 收集所有连接
	type Connection struct {
		ParentIndex int
		ChildIndex  int32
	}
	
	var connections []Connection
	for i, parent := range parentLayer {
		for _, childIndex := range parent.Children {
			connections = append(connections, Connection{
				ParentIndex: i,
				ChildIndex:  childIndex,
			})
		}
	}
	
	// 检查每对连接是否交叉
	for i := 0; i < len(connections); i++ {
		for j := i + 1; j < len(connections); j++ {
			conn1 := connections[i]
			conn2 := connections[j]
			
			// 检查两条路径是否交叉
			if isPathCrossing(conn1.ParentIndex, int(conn1.ChildIndex), 
				conn2.ParentIndex, int(conn2.ChildIndex)) {
				crossings = append(crossings, PathCrossing{
					Parent1: conn1.ParentIndex,
					Child1:  int(conn1.ChildIndex),
					Parent2: conn2.ParentIndex,
					Child2:  int(conn2.ChildIndex),
				})
			}
		}
	}
	
	return crossings
}

// 判断两条路径是否交叉
func isPathCrossing(parent1, child1, parent2, child2 int) bool {
	// 如果两条路径有相同的起点或终点，不算交叉
	if parent1 == parent2 || child1 == child2 {
		return false
	}
	
	// 检查路径是否交叉
	// 路径1: parent1 -> child1
	// 路径2: parent2 -> child2
	// 如果 parent1 < parent2 但 child1 > child2，或者相反，则路径交叉
	return (parent1 < parent2 && child1 > child2) || (parent1 > parent2 && child1 < child2)
}

// 测试特定场景的路径分配
func TestSpecificScenarios(t *testing.T) {
	fmt.Println("=== 测试特定场景 ===")
	
	// 场景1: 3个父节点，2个子节点
	testScenario(t, 3, 2, "3父2子")
	
	// 场景2: 2个父节点，5个子节点
	testScenario(t, 2, 5, "2父5子")
	
	// 场景3: 4个父节点，3个子节点
	testScenario(t, 4, 3, "4父3子")
	
	// 场景4: 1个父节点，4个子节点
	testScenario(t, 1, 4, "1父4子")
}

func testScenario(t *testing.T, parentCount, childCount int, scenarioName string) {
	fmt.Printf("\n--- %s ---\n", scenarioName)
	
	// 创建模拟的父节点和子节点
	parentLayer := make([]*MapNode, parentCount)
	childLayer := make([]*MapNode, childCount)
	
	for i := 0; i < parentCount; i++ {
		parentLayer[i] = &MapNode{
			Type:     MAP_NODE_TYPE_BATTLE,
			Children: []int32{},
		}
	}
	
	for i := 0; i < childCount; i++ {
		childLayer[i] = &MapNode{
			Type:     MAP_NODE_TYPE_BATTLE,
			Children: []int32{},
		}
	}
	
	// 分配子节点
	assignChildrenWithoutCrossing(parentLayer, childLayer)
	
	// 打印结果
	for i, parent := range parentLayer {
		fmt.Printf("父节点%d -> %v\n", i, parent.Children)
	}
	
	// 检查交叉
	crossings := detectPathCrossings(parentLayer, childLayer)
	if len(crossings) > 0 {
		t.Errorf("%s 发现路径交叉: %v", scenarioName, crossings)
	} else {
		fmt.Printf("✓ %s 无路径交叉\n", scenarioName)
	}
	
	// 检查每个父节点是否都有子节点
	for i, parent := range parentLayer {
		if len(parent.Children) == 0 {
			t.Errorf("%s 父节点%d没有子节点", scenarioName, i)
		}
	}
}

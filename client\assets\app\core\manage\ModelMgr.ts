import { error, js } from "cc"
import BaseModel from "../base/BaseModel"

// 数据模型管理
export default class ModelMgr {

    private models: Map<string, BaseModel> = new Map<string, BaseModel>()

    // 静态添加模型
    public init(map: any) {
        for (let key in map) {
            const _class = map[key]
            _class.instance = this.__add(new _class(key))
            if (mc.DEBUG) {
                window[js.getClassName(_class)] = _class
            }
        }
    }

    public create() {
        this.models.forEach(m => m.__create())
    }

    private __add(model: BaseModel) {
        if (!model.type) {
            error('__add model error. type is null!')
            return null
        }
        if (this.models.has(model.type)) {
            error('出现相同的 model. type: ' + model.type)
            return null
        }
        this.models.set(model.type, model)
        return model
    }

    // 添加模型
    public add(...params: BaseModel[]) {
        params.forEach(m => this.__add(m))
    }

    // 获取模型
    public get<T>(key: string): T {
        let model: any = this.models.get(key)
        if (!model) {
            error('get model error! not found ' + key)
        }
        return model
    }

    // 替换模型
    public reset(model: BaseModel) {
        if (!model.type) {
            return error('model type=null!')
        }
        const it = this.models.get(model.type)
        if (it) {
            it.__clean()
        }
        this.models.set(model.type, model)
    }
}
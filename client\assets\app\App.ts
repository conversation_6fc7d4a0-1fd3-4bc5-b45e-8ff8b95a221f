import { _decorator, assetManager, Asset<PERSON>anager, Component, debug, director, log, Node } from 'cc';
import { localConfig } from './script/common/LocalConfig';
import ca from '../scene/ca';
const { ccclass, property } = _decorator;

@ccclass('App')
export class App extends Component {

    private readonly WIND_NAME: string = 'login'

    onLoad() {
        // director.getStats().showStats(false)
        ut.setKeepScreenOn(true)
        // logger.open = localConfig.openLog
        // logger.openPrint = localConfig.openPrint
        // if (localConfig.RELEASE) {
        //     cc.log = function (msg: any, ...subst: any[]) { }
        // }
        log('app loload...')
    }

    start() {
        mc.init(ca.NAME_SPACE, this, {
            debug: !localConfig.RELEASE,
            lang: ca.getLocalLang(),
            changeLang: true,
            pnlIndexConf: {}
        })
        this.load()
    }

    private async load() {
        // 加载首个窗口
        await this.loadBundleByName(AssetManager.BuiltinBundleName.RESOURCES)
        await this.loadWind(this.WIND_NAME)
        // this.FindChild('LoginBg').destroy()
        // 销毁loginBg 进入第一个窗口
        eventCenter.emit(mc.Event.GOTO_WIND, this.WIND_NAME)
        // this.enabled = false
    }

    private async loadWind(wind: string) {
        return new Promise<void>(resolve => eventCenter.emit(mc.Event.PRELOAD_WIND, wind, resolve))
    }

    // private async hideLogo(it: cc.Node) {
    //     return new Promise(reslove => {
    //         cc.tween(it)
    //             .to(0.5, { opacity: 0 }, { easing: cc.easing.sineIn })
    //             .call(reslove)
    //             .start()
    //     })
    // }

    private async loadBundle(name: any) {
        return new Promise<boolean>((resolve) => {
            assetManager.loadBundle(name, (err, bundle) => {
                if (err) {
                    console.error('loadBundleError: ', name, err)
                }
                resolve(!err)
            })
        })
    }

    private async loadBundleByName(name: any) {
        while (true) {
            const success = await this.loadBundle(name)
            if (success) {
                return
            }
            await new Promise(resolve => setTimeout(resolve, 100))
        }
    }
}

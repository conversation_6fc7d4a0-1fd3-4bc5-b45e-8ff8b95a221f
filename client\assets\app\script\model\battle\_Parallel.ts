import BaseComposite from "./_BaseComposite"
import { BTState } from "./_BTConstant"

// 并行 所有子节点成功才返回成功
export default class Parallel extends BaseComposite {

    public onOpen() {
    }

    public onTick(dt: number) {
        let state = BTState.SUCCESS
        for (let i = 0, l = this.getChildrenCount(); i < l; i++) {
            let sta = this.children[i].execute(dt)
            if (sta === BTState.FAILURE) {
                state = sta
            }
        }
        return state
    }
}
// 数组
interface Array<T> {

    /**
     * 删除数组一个元素并返回这个元素
     * @param key
     * @param value
     */
    remove(key: any, value?: any): T

    /**
     * 删除满足条件的数组元素
     * @param cb 
     */
    delete(cb: (value: T, index: number) => boolean): T[]

    /**
     * 返回一个随机元素
     */
    random(): T

    /**
     * 随机删除一个元素 并返还这个元素
     */
    randomRemove(): T

    /**
     * 是否有这个元素
     */
    has(key: any, value?: any): boolean

    /**
     * 添加一个元素并返回这个数组
     */
    append(val: T): T[]

    /**
     * 添加一个元素并返回这个元素
     */
    add(val: T): T

    /**
     * 返回最后一个元素
     */
    last(): T

    /**
     * 拼接数组 对象
     */
    join2(cb: (value: T, index: number) => string, separator?: string): string

    /**
     * push数组
     */
    pushArr(arr: T[]): number;

    /**
     * push没有的
     * @param data 
     * @param key 
     * @param value 
     */
    pushNoHas(data: T, key?: any, value?: any): boolean;

    /**
     * 重新设置这个数组
     */
    set(arr: T[]): T[]

    /**
     * 从后面查找index
     * @param cb 
     */
    findLastIndex(cb: (value: T, index: number) => boolean): number

    /**
     * 从后面查找
     * @param cb 
     */
    findLast(cb: (value: T, index: number) => boolean): T

    /**
     * 打乱
     * @param cb 
     */
    shuffle(): T[]
}

// 对象
interface Object {
    values(obj: any): any[]
}
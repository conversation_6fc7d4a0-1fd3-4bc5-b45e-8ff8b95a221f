/**
 * ScrollView 扩展方法
 */

import { error, Layout, misc, Node, Prefab, ScrollView } from "cc"
import ScrollViewEx from "../component/ScrollViewEx"
import ScrollViewPlus from "../component/ScrollViewPlus"

// 填充列表
ScrollView.prototype.Items = function <T>(list: T[] | number, prefab?: any, cb?: (it: Node, data: T, i: number) => void, target?: any) {
    let i = 0, childs = this.content.children, item = childs[0]
    let count = 0
    if (typeof (list) === 'number') {
        count = list
        list = null
    } else {
        count = list.length
    }
    if (typeof (prefab) === 'function') {
        target = cb
        cb = prefab
    } else if (prefab instanceof Node || prefab instanceof Prefab) {
        item = prefab
    }
    if (!item) {
        return error('必须满足content中有一个可拷贝的节点')
    }
    const plus = this.Component(ScrollViewPlus)
    plus?.reset()
    if (plus?.isFrameRender) {
        return plus.items(list, item, cb, target)
    }
    for (let l = this.content.childrenCount; i < l; i++) {
        const it = childs[i]
        if (i < count) {
            setItemData(it, list && list[i], i, cb, target)
        } else {
            it.Data = null
            it.active = false
        }
    }
    for (; i < count; i++) {
        setItemData(mc.instantiate(item, this.content), list && list[i], i, cb, target)
    }
    // 刷新一下显示
    plus?.updateNodeShow()
}

// 添加一个
ScrollView.prototype.AddItem = function (cb: (it: Node, i: number) => void, target?: any) {
    let i = this.content!.children.findIndex(m => !m.active)
    let it = null
    if (i !== -1) {
        it = this.content!.children[i]
    } else {
        i = this.content!.childrenCount
        it = mc.instantiate(this.content!.children[0], this.content!)
    }
    it.active = true
    setItemData(it, i, undefined, cb, target)
}

function setItemData(it: Node, data: any, i: number, cb: Function, target: any) {
    it.active = true
    it.opacity = 255
    if (!cb) {
        return
    } else if (target) {
        cb.call(target, it, data, i)
    } else {
        cb(it, data, i)
    }
}

// 查找content的子节点
ScrollView.prototype.Find = function (predicate: (value: Node, index: number, obj: Node[]) => unknown, thisArg?: any): Node {
    return this.content!.children.find(predicate, thisArg)!
}

//
ScrollView.prototype.IsEmpty = function () {
    return !this.content!.children.some(m => m.active)
}

// list填充
ScrollView.prototype.List = function (len: number, cb?: (it: Node, i: number) => void, target?: any) {
    const ex = this.Component(ScrollViewEx)
    if (ex) {
        return ex.list(len, cb, target)
    } else {
        error('List error, not ScrollViewEx!')
    }
}

//
ScrollView.prototype.GetItemNode = function () {
    return this.Component(ScrollViewEx)?.getItemNode() || this.content.children[0]
}

// 将选中的移动到中间
ScrollView.prototype.SelectItemToCentre = function (index: number) {
    this.stopAutoScroll()
    if (index !== -1) {
        const lay = this.content.Component(Layout)
        lay.updateLayout()
        const width = this.content.children[0].width
        const tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft //当前位置
        const pw = this.content.parent.width
        const cx = pw * 0.5 //中间位置
        this.content.x = misc.clampf(cx - tx, Math.min(0, pw - this.content.width), 0)
    } else {
        this.scrollToLeft()
    }
}

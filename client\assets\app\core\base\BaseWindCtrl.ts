import BaseViewCtrl from "./BaseViewCtrl"

/**
 * 基础窗口
 */
export default class BaseWindCtrl extends BaseViewCtrl {

    public key: string = ''// 模块名
    public isClean: boolean = true

    public async __create() {
        this._state = 'create'
        this.__listenMaps()
        await this.onCreate()
        this.__register('create') //等创建好了 再注册
    }

    public async __ready() {
        this._state = 'ready'
        return this.onReady()
    }

    public __enter(...params: any) {
        if (this._state !== 'enter') {
            this._state = 'enter'
            this.__register('enter')
            this.onEnter(...params)
        }
    }

    public __leave() {
        this._state = 'leave'
        this.__unregister('enter')
        this.onLeave()
    }

    public __clean() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
    }

    public async onCreate() {
    }

    public async onReady() {
    }

    public onEnter(...params: any) {
    }

    public onLeave() {
    }

    public onClean() {
    }

    public isEnter() {
        return this._state === 'enter'
    }

    public setParam(opts: WindParam) {
        this.isClean = opts.isClean ?? this.isClean
    }
}
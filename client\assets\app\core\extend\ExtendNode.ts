
/**
 * Node扩展方法
 */

import BaseLocale from "../base/BaseLocale";
import { Node, js, Prefab, SwihNodeCallback, error, Size, Color, Label, RichText, UIOpacity, UITransform, Tween } from "cc";

Node.prototype.Data = null

if (!Node.prototype.hasOwnProperty('childrenCount')) {
    Object.defineProperty(Node.prototype, 'childrenCount', {
        get() {
            return this.children.length
        }
    })
}

if (!Node.prototype.hasOwnProperty('opacity')) {
    Object.defineProperty(Node.prototype, 'opacity', {
        get() {
            return this.Component(UIOpacity)?.opacity
        },
        set(val: number) {
            const cmpt = this.Component(UIOpacity)
            if (cmpt) {
                cmpt.opacity = val
            }
        }
    })
}

if (!Node.prototype.hasOwnProperty('zIndex')) {
    Object.defineProperty(Node.prototype, 'zIndex', {
        get() {
            return this.getSiblingIndex()
        },
        set(val: number) {
            this.setSiblingIndex(val)
        }
    })
}

if (!Node.prototype.hasOwnProperty('transform')) {
    Object.defineProperty(Node.prototype, 'transform', {
        get() {
            return this.Component(UITransform)
        }
    })
}

if (!Node.prototype.hasOwnProperty('height')) {
    Object.defineProperty(Node.prototype, 'height', {
        get() {
            return this.Component(UITransform)?.height || 0
        },
        set(val: number) {
            const transform = this.Component(UITransform)
            if (transform) {
                transform.height = val
            }
        }
    })
}

if (!Node.prototype.hasOwnProperty('width')) {
    Object.defineProperty(Node.prototype, 'width', {
        get() {
            return this.Component(UITransform)?.width || 0
        },
        set(val: number) {
            const transform = this.Component(UITransform)
            if (transform) {
                transform.width = val
            }
        }
    })
}

// 停止所有动画
Node.prototype.stopAllActions = function () {
    Tween.stopAllByTarget(this)
}

Node.prototype.FindChild = function (name: string | number, className?: any): any {
    name = String(name)
    let val = this
    const arr = name.split('/')
    for (let i = 0, l = arr.length; i < l; i++) {
        if (!val.isValid) {
            return null
        }
        val = val.getChildByName(arr[i])
        if (!val) {
            return null
        }
    }
    if (className) {
        val = val.getComponent(className)
    }
    return val
}

Node.prototype.Child = function (name: string | number, className?: any): any {
    if (!this.isValid) {
        return null
    }
    name = String(name)
    const cls = typeof className === 'function' ? '_' + js.getClassName(className).replace('.', '') : (className ? '_' + className : '')
    const field = '$_' + name.replace(/\//g, '_') + cls
    let val = this[field]
    if (val === undefined) {
        val = this
        if (!val.isValid) {
            return null
        }
        const arr = name.split('/')
        for (let i = 0, l = arr.length; i < l; i++) {
            val = val.getChildByName(arr[i])
            if (!val) {
                break
            }
        }
        if (val && className) {
            val = val.getComponent(className)
        }
        this[field] = !!val ? val : null
    }
    return val
}

Node.prototype.Component = function (className: any): any {
    if (!className) {
        return null
    }
    const cls = typeof className === 'function' ? js.getClassName(className).replace('.', '') : className
    const field = '$_' + cls
    let val = this[field]
    if (val === undefined) {
        val = this.getComponent(className)
        this[field] = val
    }
    return val
};

Node.prototype.Items = function <T>(list: T[] | number, prefab?: any, cb?: (it: Node, data: T, i: number) => void | any, target?: any) {
    let i = 0, childs = this.children, item = childs[0]
    let count = 0
    if (typeof (list) === 'number') {
        count = list
        list = null
    } else {
        count = list.length
    }
    if (typeof (prefab) === 'function') {
        target = cb
        cb = prefab
    } else if (prefab instanceof Node || prefab instanceof Prefab) {
        item = prefab
    }
    if (!item) {
        return logger.error('Items error, not item')
    }
    for (let l = this.children.length; i < l; i++) {
        const it = childs[i]
        if (i < count) {
            it.active = true
            setItemData(it, list ? list[i] : undefined, i, cb, target)
        } else {
            it.Data = null
            it.active = false
        }
    }
    for (; i < count; i++) {
        const it = mc.instantiate(item, this)
        it.active = true
        setItemData(it, list ? list[i] : undefined, i, cb, target)
    }
}

// 添加一个
Node.prototype.AddItem = function (prefab?: any, cb?: (it: Node, i: number) => void, target?: any): any {
    let item = null
    if (typeof (prefab) === 'function') {
        target = cb
        cb = prefab
    } else if (prefab instanceof Node || prefab instanceof Prefab) {
        item = prefab
    }
    let i = this.children.findIndex(m => !m.active), it = null
    if (item) {
        it = mc.instantiate(item, this)
    } else if (i !== -1) {
        it = this.children[i]
    } else {
        it = mc.instantiate(this.children[0], this)
    }
    it.active = true
    const index = i === -1 ? this.children.length : i
    if (!cb) {
        return { it, i }
    }
    return setItemData(it, index, undefined, cb, target)
}

function setItemData(it: Node, data: any, i: number, cb: Function, target: any) {
    if (!cb) {
        return
    } else if (target) {
        cb.call(target, it, data, i)
    } else {
        cb(it, data, i)
    }
}

// 切换节点
Node.prototype.Swih = function (val: string | number | SwihNodeCallback, reverse?: boolean, ignores?: string): Node[] {
    let name: string, cb: SwihNodeCallback
    if (typeof (val) === 'function') {
        cb = val
    } else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val)
    } else {
        return []
    }
    let arr: Node[] = []
    for (let i = 0, l = this.children.length; i < l; i++) {
        const m = this.children[i]
        if (ignores?.includes(m.name)) {
            continue
        } else if (reverse) {
            m.active = cb ? !cb(m) : (m.name !== name)
        } else {
            m.active = cb ? !!cb(m) : (m.name === name)
        }
        if (m.active) {
            arr.push(m)
        }
    }
    return arr
}

// 适应大小
Node.prototype.adaptScale = function (targetSize: Size, selfSize?: Size, maxScale: number = 1) {
    selfSize = selfSize || this.getContentSize()
    // 先算出宽度比例
    let scale = targetSize.width / selfSize.width
    // 如果高度大了 就用高的比例
    if (selfSize.height * scale > targetSize.height) {
        scale = targetSize.height / selfSize.height
    }
    this.scale = Math.min(scale, maxScale)
}

// 适应宽高
Node.prototype.adaptSize = function (targetSize: Size, selfSize?: Size, maxScale: number = 1) {
    selfSize = selfSize || this.getContentSize()
    let r = Math.min(targetSize.width / selfSize.width, maxScale)
    const h = Math.floor(r * selfSize.height)
    if (h <= targetSize.height) {
        this.width = Math.floor(r * selfSize.width)
        this.height = h
    } else {
        r = Math.min(targetSize.height / selfSize.height, maxScale)
        this.width = Math.floor(r * selfSize.width)
        this.height = Math.floor(r * selfSize.height)
    }
}

//
Node.prototype.getActive = function () {
    return this.active
}

Node.prototype.setActive = function (val: boolean) {
    this.active = val
    return val
}

// 设置颜色
Node.prototype.Color = function (val: string | Color) {
    if (!val) {
    } else if (val instanceof Color) {
        this.color = val
    } else {
        this.color = Color.WHITE.fromHEX(val)
    }
    return this
}

// 设置触摸事件穿透
Node.prototype.SetSwallowTouches = function (val: boolean) {
    if (this._touchListener) {
        this._touchListener.setSwallowTouches(val)
    }
}

Node.prototype.IsSwallowTouches = function () {
    return this._touchListener && this._touchListener.isSwallowTouches()
}

// 设置多语言key
Node.prototype.setLocaleKey = function (key: string, ...params: any[]) {
    const locale = this.Component(BaseLocale)
    if (locale) {
        locale.setKey(key, ...params)
    } else {
        error('setLocaleKey error, not LocaleComponent!')
    }
    return this.Component(Label) || this.Component(RichText)
}

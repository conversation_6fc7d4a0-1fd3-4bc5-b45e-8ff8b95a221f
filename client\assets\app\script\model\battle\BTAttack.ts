import { AnimalState } from "../../common/constant/Enums";
import BaseAction from "./_BaseAction";
import { BTState } from "./_BTConstant";

// 攻击
export default class BTAttack extends BaseAction {

    private isChangeFighterState: boolean = false //是否改变士兵的状态了
    private isChangeTargetState: boolean = false //是否改变目标士兵的状态了

    public onInit() {
    }

    public onOpen() {
        this.setTreeBlackboardData('isAttack', true) //记录已经攻击过了
        this.isChangeFighterState = false
        this.isChangeTargetState = false
    }

    public onLeave(state: BTState) {

    }

    public onTick(dt: number) {
        const attackTarget = this.ctrl.getAttackTarget(this.fighter.getCamp())
        const needAttackTime = this.getNeedAttackTime()
        let currAttackTime: number = this.getBlackboardData('currAttackTime') ?? 0
        if (currAttackTime >= needAttackTime) {
            return BTState.SUCCESS
        }
        // 增加时间
        this.setBlackboardData('currAttackTime', currAttackTime + dt)
        // 是否到击中的时间点
        const needHitTime = 420
        if (currAttackTime >= needHitTime) {
            // 击中
            if (!this.getBlackboardData('isHit')) {
                const instabilityAttackIndex = this.getBlackboardData('instabilityAttackIndex') ?? 0
                const info = this.ctrl.getAttackDamage(this.fighter, attackTarget)
                this.setBlackboardData('isHit', true)
                this.setBlackboardData('isCrit', info.isCrit)
                // const { damage, trueDamage } = attackTarget.hitPrepDamageHandle(info.damage, info.trueDamage)
                this.setBlackboardData('damage', info.damage)
                // this.setBlackboardData('trueDamage', trueDamage)
                if (!attackTarget.isDie()) {
                    const v = attackTarget.onHit(info.damage)
                    // // 处理攻击方装备效果-后置
                    // const { heal } = this.ctrl.doAttackAfter(this.target, attackTarget, {
                    //     actDamage: v.damage,
                    //     sumDamage,
                    //     trueDamage,
                    //     hitShield: v.hitShield,
                    //     time,
                    //     instabilityAttackIndex,
                    //     attackType: 'attack'
                    // })
                    // this.setBlackboardData('heal', heal + v.heal)
                    // 攻击次数
                    // this.target.addAttackCount(1)
                }
            }
            // 设置目标士兵状态
            if (!this.isChangeTargetState) {
                this.isChangeTargetState = true
                const damage = this.getBlackboardData('damage')
                const isCrit = !!this.getBlackboardData('isCrit')
                const heal = this.getBlackboardData('heal') ?? 0
                attackTarget.changeState(AnimalState.HIT, { damage, isCrit, heal, currAttackTime })
            }
        }
        // 设置士兵状态 更新视图信息
        if (!this.isChangeFighterState) {
            this.isChangeFighterState = true
            this.fighter.changeState(AnimalState.ATTACK, { currAttackTime })
        }
        return BTState.RUNNING
    }

    private getNeedAttackTime() {
        return 1000
    }
}
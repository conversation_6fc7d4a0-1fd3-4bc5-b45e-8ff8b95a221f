// @ts-nocheck

/**
 * Button扩展方法
*/

import { Button, EventHandler, EventTouch } from "cc";

// //注意这里是改写了引擎的方法，避免同一个按钮在同一帧会被调用两次
// Button.prototype._onTouchEnded = function (event: EventTouch) {
//     if (this.__pressedFlag || !this.interactable || !this.enabledInHierarchy) {
//         return
//     } else if (this._pressed) {
//         this.__pressedFlag = true
//         cc.Component.EventHandler.emitEvents(this.clickEvents, event)
//         this.node.emit('click', this)
//         ut.waitNextFrame().then(() => this.isValid && (this.__pressedFlag = false))
//     }
//     this._pressed = false
//     this._updateState()
//     event.stopPropagation()
// }

Button.prototype.setInteractableAndMF = function (val: boolean) {
    this.interactable = val
    this.Component(cc.MultiFrame).setFrame(val)
}
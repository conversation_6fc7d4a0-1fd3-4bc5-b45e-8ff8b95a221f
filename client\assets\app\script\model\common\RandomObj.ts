
// 自定义伪随机
export default class RandomObj {

    public seed: number = 0

    constructor(seed?: number) {
        this.seed = seed || Date.now()
    }

    public setSeed(seed?: number) {
        this.seed = seed || Date.now()
    }

    public numn() {
        this.seed = (this.seed * 9301 + 49297) % 233280
        let rand = Math.floor((this.seed / 233280.0) * 10000) * 0.0001
        return rand
    }

    public get(min: number, max: number) {
        if (min >= max) {
            return min
        }
        return Math.floor(this.numn() * (Math.max(max - min, 0) + 1)) + min
    }

    // 概率
    public chance(odds: number): boolean {
        if (odds <= 0) {
            return false
        }
        const mul = 100
        return this.get(0, 100 * mul - 1) < odds * mul
    }
}
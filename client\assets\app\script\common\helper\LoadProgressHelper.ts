
type Runner = (progress: LoadProgressCallback) => Promise<any>

type LoadInfo = {
    runners: Runner[]
    weight: number
}

/**
 * 进度管理器
 */
class LoadProgress {

    private _tasks: LoadInfo[] = []
    private _totalWeight: number = 0
    private _progress: LoadProgressCallback = null
    private _target: any = null

    public add(weight: number, runner: Runner) {
        this._totalWeight += weight
        this._tasks.push({
            weight,
            runners: [runner],
        })
        return this
    }

    public spawn(weight: number, runners: Runner[]) {
        this._totalWeight += weight
        this._tasks.push({
            weight,
            runners,
        })
        return this
    }

    public async run(progress: LoadProgressCallback, target?: any) {
        this._progress = progress
        this._target = target
        this.onProgress(0)
        let weight = 0
        for (let i = 0, l = this._tasks.length; i < l; i++) {
            const task = this._tasks[i]
            let percent = 1
            await Promise.all(task.runners.map(async runner => {
                await runner(p => {
                    if (p < percent) {
                        percent = p
                        const w = weight + task.weight * p
                        this.onProgress(w / this._totalWeight)
                    }
                })
            }))
            weight += task.weight
            this.onProgress(weight / this._totalWeight)
        }
    }

    private onProgress(percent: number) {
        if (!this._progress) {
        } else if (this._target) {
            this._progress.call(this._target, percent)
        } else {
            this._progress(percent)
        }
    }
}

class LoadProgressHelper {

    public create() {
        return new LoadProgress()
    }
}

export const loadProgressHelper = new LoadProgressHelper()
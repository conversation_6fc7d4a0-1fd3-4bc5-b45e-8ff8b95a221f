import { Game, game } from "cc"
import EventType from "../../common/event/EventType"
import NetEvent from "../../common/event/NetEvent"
import { viewHelper } from "../../common/helper/ViewHelper"

// 网络请求返回信息
type ResponseInfo = {
    err: string
    data?: any
}

type ReqCallbackData = {
    msgName: string
    cb: Function
    wait: boolean
}

@mc.addmodel('net')
export default class NetworkModel extends mc.BaseModel {

    private client: any = null //Socket
    private reqId: number = 0 //请求id
    private reqMap: Map<number, ReqCallbackData> = new Map<number, ReqCallbackData>() //请求列表
    private events: any[] = [] //当前事件列表

    private isEventGame: boolean = false
    private kick: boolean = false //是否被踢

    public onCreate() {
        if (!this.isEventGame) {
            this.isEventGame = true
            game.on(Game.EVENT_HIDE, this.onGameHide, this)
            if (typeof wx !== 'undefined') {
                wx.onShow(this.onGameShow.bind(this))
            } else {
                game.on(Game.EVENT_SHOW, this.onGameShow, this)
            }
        }
    }

    private onGameShow(options?: any) {
        this.emit(EventType.EVENT_GAME_SHOW, options)
    }

    private onGameHide() {
        this.emit(EventType.EVENT_GAME_HIDE)
    }

    public clean() {
        this.offAll()
        this.reset()
    }

    public setKick(val: boolean = true) {
        this.kick = val
        this.offAll()
    }

    public reset() {
        this.kick = false
        this.reqId = 0
        this.cleanReqMap()
    }

    private cleanReqMap() {
        this.reqMap.forEach(m => {
            this.showNetReqEnd(m.wait)
            m.cb && m.cb({ err: 'login.net_error' })
        })
        this.reqMap.clear()
    }

    private async doconnect(prop: any) {
        return new Promise<boolean>(resolve => {
            const clientId = 'mqttjs_' + Math.random().toString(16).substring(2, 10)
            this.client = new Paho.MQTT.Client(prop.host, prop.port, '/mqtt', clientId)
            this.client.connect({
                onSuccess: (evt: any) => { //连接成功
                    console.log('mqant connect success!')
                    this.reset()
                    resolve(true)
                },
                onFailure: (evt: any) => { //连接错误
                    console.log('mqant connect failure!')
                    // wxHelper.errorAndFilter('connect fail', evt?.errorCode, evt?.errorMessage)
                    resolve(false)
                },
                mqttVersion: 3,
                useSSL: !!prop.useSSL,
                cleanSession: true,
                keepAliveInterval: 30, //心跳
            })
        })
    }

    // 连接网络
    public async connect(prop: any) {
        console.log('mqant connect:', prop.host + ':' + prop.port)
        this.close()
        const ok = await this.doconnect(prop)
        if (ok) {
            // 注册连接断开处理事件
            this.client.onConnectionLost = (evt: any) => {
                if (evt.errorCode === 0 || evt.errorCode === 5) {
                    return
                }
                this.cleanReqMap()
                this.client = null
                this.emit('disconnect', evt)
                console.log('mqant disconnect.')
            }
            // 注册消息接收处理事件
            this.client.onMessageArrived = this.recvMessage.bind(this)
            // 打印ping
            // this.client._setTrace(({ severity, message }) => {
            //     if (message.includes('Pinger.doPing')) {
            //         cc.log(new Date(), message)
            //     }
            // })
        }
        return ok
    }

    // 关闭网络
    public close() {
        if (this.client) {
            this.offAll()
            if (this.isConnected()) {
                this.client.disconnect()
            }
            this.client = null
            console.log('mqant close.')
        }
    }

    public isConnected() {
        return !!this.client?.isConnected()
    }

    // 主动ping
    public ping() {
        this.client?.ping()
    }

    // 设置心跳
    public setKeepAliveInterval(val: number) {
        this.client?.setKeepAliveInterval(val)
    }

    private showNetReqBegin(show: boolean) {
        if (show) {
            this.emit(NetEvent.NET_REQ_BEGIN)
        }
    }

    private showNetReqEnd(show: boolean) {
        if (show) {
            this.emit(NetEvent.NET_REQ_END)
        }
    }

    // 发送
    public send(route: string, msg?: any, cb?: (ret: ResponseInfo) => void, wait?: boolean) {
        if (!route || !this.isConnected()) {
            return this.emit(NetEvent.NET_DISCONNECT)
        } else if (arguments.length === 2 && typeof msg === 'function') {
            cb = msg
            msg = {}
        } else {
            msg = msg || {}
        }
        const msgName = route.replace('/', '_').toUpperCase()
        const cls = proto[msgName + '_C2S']
        if (!cls) {
            return
        } else if (cb) {
            this.reqId += 1
            this.reqMap.set(this.reqId, { cb: cb, wait: !!wait, msgName })
            route = route + '/' + this.reqId
        }
        this.showNetReqBegin(!!wait)
        this.client.send(route, cls.encode(msg).finish(), 1)
    }

    public async request(route: string, msg?: any, wait?: boolean) {
        if (!route || !this.isConnected()) {
            this.emit(NetEvent.NET_DISCONNECT)
            return { err: 'login.net_error' }
        } else if (typeof (msg) === 'boolean') {
            wait = msg
            msg = {}
        } else if (!msg) {
            msg = {}
        }
        const msgName = route.replace('/', '_').toUpperCase()
        const cls = proto[msgName + '_C2S']
        if (!cls) {
            console.error('route error, msgName=' + msgName + '_C2S')
            return { err: 'toast.route_error' }
        }
        return new Promise<ResponseInfo>(resolve => {
            this.reqId += 1
            this.reqMap.set(this.reqId, { cb: resolve, wait: !!wait, msgName })
            route = route + '/' + this.reqId
            this.showNetReqBegin(!!wait)
            this.client.send(route, cls.encode(msg).finish(), 1)
        })
    }

    private recvMessage(evt: any) {
        const [moduleType, func, msgid] = evt.destinationName.split('/')
        // cc.log(moduleType, func, evt.payloadBytes.length + 'B')
        if (msgid) {
            const id = parseInt(msgid)
            const req = this.reqMap.get(id)
            if (req) {
                this.reqMap.delete(id)
                this.showNetReqEnd(req.wait)
                const body = proto.S2C_RESULT.decode(evt.payloadBytes)
                let err = body.error
                if (!err) {
                } else if (err.startsWith('Service(type') && err.endsWith('not found')) {
                    err = 'login.net_error'
                } else if (err === 'deadline exceeded') {
                    err = 'login.net_timeout'
                } else if (err.includes('runtime error')) {
                    err = 'login.net_runtime_error'
                }
                try {
                    let data = proto[req.msgName + '_S2C']?.decode(body.data)
                    data = data?.toJSON()
                    req.cb && req.cb({ err, data })
                } catch (error) {
                    req.cb && req.cb({ err: 'login.net_parse_error', data: null })
                }
            }
        } else if (moduleType && func) {
            const msgName = moduleType.toUpperCase() + '_' + func.toUpperCase() + '_NOTIFY'
            const cls = proto[msgName]
            if (cls) {
                try {
                    let data = cls.decode(evt.payloadBytes)
                    data = data.toJSON()
                    this.emit(moduleType + '/' + func, data)
                } catch (error) {
                    // errorReportHelper.reportError('recvMessage NOTIFY Error', { route: msgName, error })
                    console.error(error)
                }
            } else {
                console.error('error NOTIFY route:', msgName)
                // errorReportHelper.reportError('recvMessage NOTIFY Not route', { route: msgName })
            }
        } else {
            console.error('recvMessage error msg:', evt.destinationName)
            // errorReportHelper.reportError('recvMessage error', { destinationName: evt.destinationName })
        }
    }

    // 监听
    public on(route: string, cb: Function, target?: any) {
        eventCenter.off(route, cb, target)
        eventCenter.on(route, cb, target)
        this.events.push({ route, cb, target })
    }

    // 注销监听
    public off(route: string, cb?: Function, target?: any) {
        eventCenter.off(route, cb, target)
        this.events.delete(m => m.route === route && m.cb.toString() == m.cb.toString() && m.target == m.target)
    }

    public offAll() {
        this.events.forEach(m => eventCenter.off(m.route, m.cb, m.target))
        this.events.length = 0
    }

    public isKick() {
        return this.kick
    }

    // http请求
    public async post(opts: { url: string, data?: any, retryCount?: number, wait?: boolean, showConnectFail?: boolean }) {
        let cnt = 0, max = opts?.retryCount || 5
        this.showNetReqBegin(opts.wait)
        while (true) {
            const res = await ut.httpRequest('POST', opts.url, opts.data || {})
            if (res?.status === 0 || res?.status === 200) {
                this.showNetReqEnd(opts.wait)
                return res.data
            } else if (cnt < max) {
                cnt += 1
                await ut.wait(2)
            } else if (opts.showConnectFail) {
                cnt = 0
                max += 2
                this.showNetReqEnd(opts.wait)
                const ok = await viewHelper.showConnectFail()
                if (!ok) {
                    return null
                }
                this.showNetReqBegin(opts.wait)
            } else {
                this.showNetReqEnd(opts.wait)
                return null
            }
        }
    }
}
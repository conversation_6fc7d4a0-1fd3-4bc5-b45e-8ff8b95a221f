import { _decorator, Component, Node, tween, view } from 'cc';
const { ccclass, property } = _decorator;

@ccclass
export class LoadingMaskCmpt extends Component {

    private root: Node = null
    private targetX: number = 0

    onLoad() {
        this.root = this.FindChild('root')
        this.targetX = this.root.transform.width = view.getVisibleSize().width * 0.5
        this.root.x = this.targetX
    }

    public async run() {
        this.root.x = this.targetX
        return new Promise(resolve => {
            tween(this.root).to(0.3, { x: 0 }).call(resolve).start()
        })
    }

    public async stop() {
        this.root.x = 0
        return new Promise(resolve => {
            tween(this.root).to(0.3, { x: this.targetX }).call(resolve).start()
        })
    }
}

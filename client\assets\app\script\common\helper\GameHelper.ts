import { sys } from "cc"
import GameModel from "../../model/game/GameModel"
import { localConfig } from "../LocalConfig"
import ca from "db://assets/scene/ca"
import UserModel from "../../model/common/UserModel"
import NetworkModel from "../../model/common/NetworkModel"

/**
 * 游戏逻辑帮助类
 */
class GameHelper {

    public get net() { return NetworkModel.ins<NetworkModel>() }
    public get game() { return GameModel.ins<GameModel>() }
    public get user() { return UserModel.ins<UserModel>() }

    public getAppType() { return ca.getAppType() }
    public isInland() { return ca.getAppType() === 'inland' }
    public isGLobal() { return ca.getAppType() === 'global' }

    // 获取服务器地址
    public getServerUrl() {
        // if (!this.isRelease) {
        //     return localConfig.server_test
        // } else if (this.isGLobal()) {
        //     return localConfig.servers[this.getServerArea()] || localConfig.servers.hk
        // } else if (!ut.isWechatGame() || wxHelper.isRelease()) {
        //     return localConfig.servers.china
        // }
        // return localConfig.server_test
        return localConfig.server_test
    }

    // 获取http服务器url
    public getHttpServerUrl(): string {
        // if (!this.isRelease) {
        //     return localConfig.httpServerUrl.test
        // }
        // return localConfig.httpServerUrl[this.getServerArea()] || localConfig.httpServerUrl.hk
        return localConfig.httpServerUrl.test
    }

    // 获取运行平台
    public getRunPlatform() {
        if (ut.isMobile()) {
            return ut.isAndroid() ? 'android' : 'ios'
        } else if (ut.isMiniGame()) {
            return typeof qq != 'undefined' ? 'qq' : 'wx'
        }
        return 'none'
    }

    // 获取商店平台
    public getShopPlatform() {
        const isGLobal = this.isGLobal()
        if (ut.isIos()) {
            return isGLobal ? 'ios_global' : 'ios_inland'
        } else if (ut.isAndroid()) {
            return isGLobal ? 'google' : 'taptap'
        } else if (sys.isBrowser) {
            return 'web'
        }
        return sys.os + '_' + sys.platform
    }
}

export const gHelper = new GameHelper()
if (sys.isBrowser) {
    window['gHelper'] = gHelper
}
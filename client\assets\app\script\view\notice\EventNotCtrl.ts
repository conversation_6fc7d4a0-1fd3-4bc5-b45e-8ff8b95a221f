import { _decorator, log } from "cc";
import GameModel from "../../model/game/GameModel";
const { ccclass } = _decorator;

@ccclass
export default class EventNotCtrl extends mc.BaseNoticeCtrl {

    //@autocode property begin
    //@end

    // private net: 
    private game: GameModel = null

    private isDisconnect: boolean = false //是否断开连接

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.game = GameModel.ins()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    update(dt: number) {
        // if (this.net.isKick()) {
        //     return
        // }
        if (this.isDisconnect) {
            return
        }
        // this.game.update(dt)
    }
}

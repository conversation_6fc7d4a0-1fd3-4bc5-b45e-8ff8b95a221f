
import { AlertOpts, MessageBoxOpts } from "../constant/DataType"
import NetEvent from "../event/NetEvent"
import NotEvent from "../event/NotEvent"

import { sys } from "cc"

/**
 * 视图帮助方法
 */
class ViewHelper {

    // 跳转场景
    public async gotoWind(val: string, ...params: any) {
        return new Promise<void>(resolve => eventCenter.emit(mc.Event.GOTO_WIND, val, resolve, ...params))
    }

    // 预加载场景
    public async preloadWind(key: string, progress?: (percent: number) => void) {
        return new Promise<void>(resolve => eventCenter.emit(mc.Event.PRELOAD_WIND, key, resolve, progress))
    }

    // 预加载UI
    public async preloadPnl(key: string, progress?: (done: number, total: number) => void) {
        return new Promise<mc.BasePnlCtrl>(resolve => eventCenter.emit(mc.Event.PRELOAD_PNL, key, resolve, progress))
    }

    // 显示UI
    public async showPnl(key: string | mc.BasePnlCtrl, ...params: any) {
        return new Promise<mc.BasePnlCtrl>(resolve => eventCenter.emit(mc.Event.OPEN_PNL, key, resolve, ...params))
    }

    // 隐藏UI
    public hidePnl(key: string | mc.BasePnlCtrl) {
        eventCenter.emit(mc.Event.HIDE_PNL, key)
    }

    // 显示提示框
    public showAlert(msg: string, opts?: AlertOpts) {
        eventCenter.emit(NotEvent.OPEN_ALERT, msg, opts)
    }

    // 显示对话框
    public showMessageBox(msg: string, opts?: MessageBoxOpts) {
        eventCenter.emit(NotEvent.OPEN_MESSAGE_BOX, msg, opts)
    }

    // 主动关闭对话框
    public hideMessageBox() {
        eventCenter.emit(NotEvent.HIDE_MESSAGE_BOX)
    }

    // 显示说明
    public showDesc(text: string, params?: any[]) {
        this.showPnl('common/Desc', { text, params })
    }

    // 显示说明信息
    public showDescInfo(title: string, list: { key: string, params?: any[] }[]) {
        this.showPnl('common/DescInfo', { title, list })
    }

    // 显示网络等待
    public showNetWait(val: boolean, delay?: number) {
        if (val) {
            eventCenter.emit(NetEvent.NET_REQ_BEGIN, delay)
        } else {
            eventCenter.emit(NetEvent.NET_REQ_END)
        }
    }

    // 显示连接失败
    public async showConnectFail() {
        return new Promise<boolean>(resolve => {
            this.showMessageBox('login.connect_server_fail', {
                lockClose: true,
                okText: 'login.button_retry',
                ok: () => resolve(true),
                cancel: () => resolve(false),
            })
        })
    }
}

export const viewHelper = new ViewHelper()
if (sys.isBrowser) {
    window['viewHelper'] = viewHelper
}
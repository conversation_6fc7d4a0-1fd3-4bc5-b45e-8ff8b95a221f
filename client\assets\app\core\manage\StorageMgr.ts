
// 存储封装
class StorageMgr {

    private __key(key: string): string {
        return mc.GameNameSpace + '_' + key
    }

    private __get(key: string) {
        return localStorage.getItem(this.__key(key))
    }

    private __set(key: string, val: string) {
        localStorage.setItem(this.__key(key), val)
    }

    public loadString(key: string) {
        const val = this.__get(key)
        return val;
    }
    public saveString(key: string, val: string) {
        this.__set(key, val)
    }

    public loadNumber(key: string) {
        const val = this.__get(key)
        return val ? Number(val) : null
    }
    public saveNumber(key: string, val: number) {
        this.__set(key, String(val))
    }

    public loadBool(key: string) {
        const val = this.__get(key)
        return val ? val === '1' : null
    }
    public saveBool(key: string, val: boolean) {
        this.__set(key, val ? '1' : '0')
    }

    public loadJson(key: string) {
        const val = this.__get(key)
        return val ? JSON.parse(val) : null
    }
    public saveJson(key: string, val: any) {
        this.__set(key, JSON.stringify(val))
    }

    public reset() {
    }

    public clear() {
        this.reset()
        localStorage.clear()
    }
}

window['storageMgr'] = new StorageMgr()
import { _decorator, log } from "cc";
import { viewHelper } from "../../common/helper/ViewHelper";
const { ccclass } = _decorator;

@ccclass
export default class PnlWaitNotCtrl extends mc.BaseNoticeCtrl {

    //@autocode property begin
    //@end

    private opening: boolean = false
    private delay: number = 0.5// 延迟多少秒显示画面
    private hideTime: number = 15// 延迟多少秒后强行关闭界面
    private elapsed: number = 0

    private loadInfo: LoadPnlInfo = null //当前加载信息

    public listenEventMaps() {
        return [
            { [mc.Event.LOAD_BEGIN_PNL]: this.onEventOpen },
            { [mc.Event.LOAD_END_PNL]: this.onEventHide },
        ]
    }

    public async onCreate() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onEventOpen(info: LoadPnlInfo) {
        this.open()
        this.loadInfo = info
        this.opening = true
        // this.maskNode_.active = true
        // this.roootNode_.active = false
        this.elapsed = 0
    }

    private onEventHide() {
        this.hide()
        this.opening = false
        // this.maskNode_.active = false
        // this.roootNode_.active = false
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    update(dt: number) {
        if (!this.opening) {
            return
        }
        this.elapsed += dt
        if (this.elapsed >= this.delay && this.elapsed < this.hideTime) {
            this.elapsed += this.hideTime
            // this.roootNode_.active = true
        }
        if (this.elapsed - this.hideTime >= this.hideTime) {
            mc.unlockTouch('__show_pnl__')
            if (this.loadInfo?.id) {
                const { id, name, params } = this.loadInfo
                this.emit(mc.Event.GIVEUP_LOAD_PNL, id) //放弃加载
                viewHelper.showMessageBox('toast.load_timeout', {
                    ok: () => viewHelper.showPnl(name, ...params),
                    cancel: () => { },
                    okText: 'login.button_retry',
                })
            } else {
                viewHelper.showMessageBox('toast.load_timeout')
            }
            this.onEventHide()
        }
    }
}

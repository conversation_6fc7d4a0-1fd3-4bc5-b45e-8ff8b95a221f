import { _decorator, CCBoolean, CCInteger, Component, Node, NodeEventType, ScrollView, UITransform, v3, Vec3 } from "cc";

const { ccclass, property, menu, requireComponent, disallowMultiple } = _decorator;

@ccclass
@disallowMultiple()
@requireComponent(ScrollView)
@menu('自定义组件/ScrollViewPlus')
export default class ScrollViewPlus extends Component {

    @property(CCBoolean)
    public isFrameRender: boolean = true //是否开启分帧渲染
    @property({ type: CCInteger, range: [1, 10, 1], tooltip: '多少帧渲染一次', slide: true })
    private updateRate: number = 1 //渲染频率 多少帧渲染一个
    @property({ type: CCInteger, range: [0, 30, 1], tooltip: '一次渲染多少个,0渲染所有', slide: true })
    private updateRenderCount: number = 1 //分帧渲染个数

    private scrollView: ScrollView = null
    private viewNode: Node = null
    private contentTransform: UITransform = null
    private viewTransform: UITransform = null
    private content: Node = null
    private item: Node = null

    private frameCount: number = 0 //多少帧
    private renderStartIndex: number = 0 //渲染起点下标
    private needRenderCount: number = 0 //需要渲染的item个数
    private currRenderCount: number = 0 //当前渲染的item个数

    private setItemCallback: Function = null
    private callbackTarget: any = null
    private datas: any[] = null
    private isCanScrollingUpdate: boolean = false //是否可以滚动刷新了
    private canScrollingUpdate: boolean = false

    private _temp_vec2_1: Vec3 = v3()
    private _temp_vec2_2: Vec3 = v3()

    onLoad() {
        this.init()
    }

    private init() {
        if (this.scrollView) {
            return
        }
        this.scrollView = this.getComponent(ScrollView)
        this.content = this.scrollView.content
        this.viewNode = this.content.parent
        this.contentTransform = this.content.getComponent(UITransform)
        this.viewTransform = this.viewNode.getComponent(UITransform)
        this.item = this.content.children[0]
        if (this.item) {
            this.item.active = false
        }
        this.content.on(NodeEventType.CHILD_REMOVED, this.onScrolling, this)
        this.content.on(NodeEventType.CHILDREN_ORDER_CHANGED, this.onScrolling, this)
        this.node.on('scrolling', this.onScrolling, this)
    }

    // 滚动
    private onScrolling() {
        if (!this.scrollView || this.content.childrenCount === 0 || !this.isCanScrollingUpdate) {
            return
        }
        // log('111 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.childrenCount)
        const contentX = this.contentTransform.width * this.contentTransform.anchorX
        const contentY = this.contentTransform.height * this.contentTransform.anchorY
        const pos = this.content.getPosition(this._temp_vec2_1)
        pos.x += this.viewTransform.width * this.viewTransform.anchorX - contentX
        pos.y += this.viewTransform.height * this.viewTransform.anchorY - contentY
        // 遍历 ScrollView Content 内容节点的子节点，对每个子节点的包围盒做和 ScrollView 可视区域包围盒做碰撞判断
        this.content.children.forEach(node => {
            if (!node.active) {
                return
            }
            const p = node.getPosition(this._temp_vec2_2)
            const transform = node.Component(UITransform)
            p.x = pos.x + p.x + (contentX - transform.width * transform.anchorX)
            p.y = pos.y + p.y + (contentY - transform.height * transform.anchorY)
            if (p.x < -transform.width || p.x > this.viewTransform.width || p.y < -transform.height || p.y > this.viewTransform.height) { //如果没有相交就隐藏
                if (node.opacity !== 0) {
                    node.opacity = 0
                }
            } else if (node.opacity !== 255) {
                node.opacity = 255
            }
        })
        // log('222 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.childrenCount)
    }

    update(dt: number) {
        // 渲染
        if (!this.item) {
            return
        } else if (this.isFrameRender && this.currRenderCount < this.needRenderCount) {
            this.frameCount += 1
            if (this.frameCount >= this.updateRate) {
                this.frameCount = 0
                this.updateItems()
            }
        } else if (this.canScrollingUpdate && !this.isCanScrollingUpdate) {
            this.isCanScrollingUpdate = true
            this.onScrolling()
        }
    }

    // 开始创建item
    private updateItems() {
        let cnt = this.updateRenderCount > 0 ? this.updateRenderCount : this.needRenderCount, cur = 0
        if (this.currRenderCount + cnt > this.needRenderCount) {
            cnt = this.needRenderCount - this.currRenderCount
        }
        let i = this.renderStartIndex
        for (; i < this.needRenderCount && cur < cnt; i++) {
            this.setItemData(this.content.children[i] || mc.instantiate(this.item, this.content), i)
            cur += 1
        }
        this.currRenderCount += cnt
        this.renderStartIndex = i
        if (this.currRenderCount >= this.needRenderCount) {
            // log('updateItems done...', i, this.content.childrenCount)
            // 将剩余的隐藏
            for (let l = this.content.childrenCount; i < l; i++) {
                const node = this.content.children[i]
                node.active = false
                node.Data = null
            }
        }
    }

    private setItemData(it: Node, i: number) {
        it.active = true
        it.Data = null
        it.opacity = 255
        if (!this.setItemCallback) {
        } else if (this.callbackTarget) {
            this.setItemCallback.call(this.callbackTarget, it, this.datas?.[i], i)
        } else {
            this.setItemCallback(it, this.datas?.[i], i)
        }
        return it
    }

    public reset() {
        this.isCanScrollingUpdate = false
        this.canScrollingUpdate = false
    }

    public items<T>(list: T[] | number, prefab?: any, cb?: (it: Node, data: T, i: number) => void, target?: any) {
        if (typeof (list) === 'number') {
            this.needRenderCount = list
            this.datas = null
        } else {
            this.needRenderCount = list.length
            this.datas = list
        }
        if (prefab) {
            this.item = prefab
        }
        this.setItemCallback = cb
        this.callbackTarget = target
        this.currRenderCount = 0
        this.renderStartIndex = 0
        this.canScrollingUpdate = true
        if (this.needRenderCount === 0) {
            this.content.children.forEach(m => {
                m.active = false
                m.Data = null
            })
        }
    }

    public updateNodeShow() {
        this.canScrollingUpdate = true
    }
}

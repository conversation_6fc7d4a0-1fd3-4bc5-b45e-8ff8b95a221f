
export default {
    MVC_LOGGER_PRINT:       'MVC_LOGGER_PRINT_101', //打印事件
    MVC_ERROR_MSG:          'MVC_ERROR_MSG_102', //错误
    LANGUAGE_CHANGED:       'LANGUAGE_CHANGED_103', //语言切换

    LOADING_WAIT_BEGIN:     'LOADING_WAIT_BEGIN_501', //通用等待
    LOADING_WAIT_END:       'LOADING_WAIT_END_502',

    OPEN_PNL:               'OPEN_PNL_201',
    HIDE_PNL:               'HIDE_PNL_202',
    HIDE_ALL_PNL:           'HIDE_ALL_PNL_203',
    CLOSE_PNL:              'CLOSE_PNL_204',
    CLOSE_ALL_PNL:          'CLOSE_ALL_PNL_205',
    CLOSE_MOD_PNL:          'CLOSE_MOD_PNL_206',
    PRELOAD_PNL:            'PRELOAD_PNL_207',
    LOAD_BEGIN_PNL:         'LOAD_BEGIN_PNL_208',
    LOAD_END_PNL:           'LOAD_END_PNL_209',
    PNL_ENTER:              'PNL_ENTER_210',
    PNL_LEAVE:              'PNL_LEAVE_211',
    PNL_ENTER_PLAY_DONE:    'PNL_ENTER_PLAY_DONE_215', //pnl打开时 播放动作完成
    CLEAN_ALL_UNUSED:       'CLEAN_ALL_UNUSED_212',
    GIVEUP_LOAD_PNL:        'GIVEUP_LOAD_PNL_213',
    CLEAN_LOAD_PNL_QUEUE:   'CLEAN_LOAD_PNL_QUEUE_214',

    GOTO_WIND:              'GOTO_WIND_301',
    PRELOAD_WIND:           'PRELOAD_WIND_302',
    WIND_ENTER:             'WIND_ENTER_303',
    CLEAN_CACHE_WIND:       'CLEAN_CACHE_WIND_304',
    LOAD_BEGIN_WIND:        'LOAD_BEGIN_WIND_305',
    LOAD_END_WIND:          'LOAD_END_WIND_306',
    READY_BEGIN_WIND:       'READY_BEGIN_WIND_307',
    READY_END_WIND:         'READY_END_WIND_308',

    LOAD_ALL_NOTICE:        'LOAD_ALL_NOTICE_401',
    LOAD_NOTICE:            'LOAD_NOTICE_402',
}
import { EDITOR } from "cc/env";
import BasePnlCtrl from "../base/BasePnlCtrl";
import CoreEventType from "../event/CoreEventType";
import { _decorator, Button, Component, Enum, error, EventTouch, Node, tween, Tween, v3, Vec3 } from "cc";

const { ccclass, property, menu, requireComponent, executeInEditMode } = _decorator;

enum SoundType {
    NONE,
    DEFAULT,
    CUSTOM,
}

enum EventType {
    NONE,
    OPEN_PNL,
    HIDE_PNL,
    GOTO_WIND,
    CUSTOM,
}

@ccclass
@executeInEditMode
@requireComponent(Button)
@menu('自定义组件/ButtonEx')
export default class ButtonEx extends Component {

    @property({ range: [0, 10] })
    private duration: number = 0.1
    @property(Vec3)
    private positionOffset: Vec3 = v3()
    // 点击音效
    @property({ type: Enum(SoundType) })
    private sound: SoundType = SoundType.NONE //音效
    @property({ visible: function () { return this.sound === SoundType.CUSTOM } })
    private soundPath: string = '' //音效路径
    @property({ visible: function () { return this.sound === SoundType.CUSTOM } })
    private soundVolume: number = 1.0 //音量

    // 点击事件
    @property({ type: Enum(EventType) })
    private event: EventType = EventType.NONE //事件类型
    // @ts-ignore
    @property({ visible: function () { return this.event === EventType.CUSTOM } })
    private eventName: string = '' //事件名字
    // @ts-ignore
    @property({ visible: function () { return this.event !== EventType.NONE } })
    private eventParams: string = '' //事件参数

    private button: Button = null!
    private target: Node = null!
    private __originalPosition: Vec3 = null!

    private isPlayAct: boolean = true// 是否需要播放动作
    private isDown: boolean = false// 是否按下
    private touchId: number = -1

    private tempPnl: BasePnlCtrl | null = null

    public static DefaultClickPath: string = ''// 默认点击音效

    private __pressedFlag: boolean = false

    onLoad() {
        this.button = this.getComponent(Button)!
        this.target = this.button.target ? this.button.target : this.node
        this.isPlayAct = !this.positionOffset.equals(Vec3.ZERO)
        // 监听事件
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
    }

    public get originalPosition() {
        if (!this.__originalPosition) {
            this.__originalPosition = this.target.getPosition()!
        }
        return this.__originalPosition
    }

    onEnable() {
        if (!EDITOR && this.isPlayAct && this.__originalPosition) {
            Tween.stopAllByTarget(this.target)
            this.target.setPosition(this.__originalPosition)
        }
    }

    // 是否可点击
    public set interactable(val: boolean) {
        this.button.interactable = val
    }
    public get interactable() {
        return this.button.interactable
    }

    // 触摸开始
    private onTouchStart(event: EventTouch) {
        if (!this.interactable || this.touchId !== -1) {
            return
        }
        this.touchId = event.getID()!
        this.down()
    }

    // 触摸移动
    private onTouchMove(event: EventTouch) {
        if (!this.interactable || this.touchId !== event.getID()) {
            return
        }
        const hit = this.node.transform.isHit(event.getUILocation())
        if (hit) {
            if (!this.isDown) {
                this.down()
            }
        } else {
            if (this.isDown) {
                this.restore()
            }
        }
    }

    // 触摸结束
    private onTouchEnd(event: EventTouch) {
        if (!this.interactable || this.touchId !== event.getID()) {
            return
        }
        if (this.__pressedFlag) {
            return
        }

        this.touchId = -1
        this.restore()
        // 播放音效
        this.playSound()
        // 发送事件
        this.emit()

        this.__pressedFlag = true
        ut.waitNextFrame().then(() => this.isValid && (this.__pressedFlag = false))
    }

    // 触摸取消
    private onTouchCancel(event: EventTouch) {
        if (!this.interactable || this.touchId !== event.getID()) {
            return
        }
        this.touchId = -1
        this.restore()
    }

    // 按下
    private down() {
        this.isDown = true
        if (this.isPlayAct) {
            this.target.setPosition(this.originalPosition)
            tween(this.target).by(this.duration, { position: this.positionOffset }).start()
        }
    }

    // 还原
    private restore() {
        this.isDown = false
        if (this.isPlayAct) {
            tween(this.target).to(this.duration, { position: this.originalPosition }).start()
        }
    }

    // 播放声音
    private playSound() {
        if (this.sound === SoundType.NONE) {
            return
        }
        let url = this.sound === SoundType.DEFAULT ? ButtonEx.DefaultClickPath : this.soundPath
        if (url) {
            // audioMgr.playSFX(url, this.soundVolume)
        }
    }

    // 发送事件
    private emit() {
        if (this.event === EventType.NONE) {
            return
        }
        if (this.event === EventType.HIDE_PNL) {
            if (this.eventParams) {
                return eventCenter.emit(CoreEventType.HIDE_PNL, this.eventParams)
            }
            if (this.tempPnl) {
                return eventCenter.emit(CoreEventType.HIDE_PNL, this.tempPnl)
            }
            let node = this.node
            this.tempPnl = node.getComponent(BasePnlCtrl)
            while (!this.tempPnl) {
                node = node.parent!
                this.tempPnl = node && node.getComponent(BasePnlCtrl)
            }
            if (this.tempPnl) {
                eventCenter.emit(CoreEventType.HIDE_PNL, this.tempPnl)
            } else {
                error('button event [HIDE_PNL] not pnl?')
            }
        } else if (this.event === EventType.CUSTOM) {
            eventCenter.emit(this.eventName, this.eventParams || this.target)
        } else if (this.event === EventType.OPEN_PNL) {
            eventCenter.emit(CoreEventType.OPEN_PNL, this.eventParams)
        } else if (this.event === EventType.GOTO_WIND) {
            eventCenter.emit(CoreEventType.GOTO_WIND, this.eventParams)
        } else {
            error(`button event not ${this.event}`)
        }
    }
}

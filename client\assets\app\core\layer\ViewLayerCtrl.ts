import { Layers, _decorator } from "cc";
import BaseLayerCtrl from "../base/BaseLayerCtrl";
import ViewCtrlMgr from "../manage/ViewCtrlMgr";
import BasePnlCtrl from "../base/BasePnlCtrl";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property } = _decorator;

@ccclass
export default class ViewLayerCtrl extends BaseLayerCtrl {

    private ctrlMgr: ViewCtrlMgr = null!

    public listenEventMaps() {
        return [
            { [CoreEventType.OPEN_PNL]: this.onOpenPnl },
            { [CoreEventType.HIDE_PNL]: this.onHidePnl },
            { [CoreEventType.HIDE_ALL_PNL]: this.onHideAllPnl },
            { [CoreEventType.CLOSE_PNL]: this.onClosePnl },
            { [CoreEventType.CLOSE_ALL_PNL]: this.onCloseAllPnl },
            { [CoreEventType.CLOSE_MOD_PNL]: this.onCloseModPnl },
            { [CoreEventType.PRELOAD_PNL]: this.onPreloadPnl },
            { [CoreEventType.CLEAN_ALL_UNUSED]: this.onCleanAllUnused },
            { [CoreEventType.GIVEUP_LOAD_PNL]: this.onGiveupLoadPnl },
        ]
    }

    public onCreate() {
        this.node.layer = Layers.Enum.UI_2D
    }

    public onClean() {

    }
    
    public setCtrlMgr(mgr: ViewCtrlMgr) {
        this.ctrlMgr = mgr
        this.ctrlMgr.node = this.node
    }

    public setPnlIndexConf(conf: any) {
        this.ctrlMgr.setPnlIndexConf(conf)
    }

    public getOpenPnls() {
        return this.ctrlMgr.getOpened()
    }

    private onOpenPnl(key: string | BasePnlCtrl, ...params: any) {
        this.ctrlMgr.show(key, ...params)
    }

    private onHidePnl(key: string | BasePnlCtrl) {
        this.ctrlMgr.hide(key)
    }

    private onHideAllPnl(val?: string) {
        this.ctrlMgr.hideAll(val)
    }

    private onClosePnl(key: string | BasePnlCtrl) {
        this.ctrlMgr.clean(key)
    }

    private onCloseAllPnl(val?: string) {
        this.ctrlMgr.cleanAll(val)
    }

    private onCloseModPnl(mod: string) {
        this.ctrlMgr.cleanByMod(mod)
    }

    private onPreloadPnl(key: string, complete?: Function, progress?: (done: number, total: number) => void) {
        this.ctrlMgr.preloadPnl(key).then(pnl => {
            complete && complete(pnl);
            progress && progress(1, 1);
        })
    }

    private onCleanAllUnused() {
        this.ctrlMgr.cleanAllUnused()
    }

    private onGiveupLoadPnl(id: number) {
        this.ctrlMgr.giveupLoadById(id)
    }

    private onCleanLoadPnlQueue(isUnlock?: boolean) {
        this.ctrlMgr.cleanLoadQueue(isUnlock)
    }
}
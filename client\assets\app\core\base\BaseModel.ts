
// 基础数据模型
export default class BaseModel {

    public type: string = ''

    constructor(type: string) {
        this.type = type
    }

    public __create() {
        this.onCreate()
    }

    public __clean() {
        this.onClean()
    }

    public onCreate() {
    }

    public onClean() {
    }

    public emit(type: string | number, ...params: any) {
        eventCenter.emit(type, ...params)
    }

    public getModel<T>(key: string): T {
        return mc.getModel<T>(key)
    }

    public static instance: any = null
    public static ins() { return this.instance }
}

/**
 * Animation扩展方法
 */

import { Animation, Skeleton } from "cc";

Animation.prototype.playAsync = async function (name?: string, startTime?: number) {
    return new Promise(resolve => {
        this.once('finished', resolve)
        this.play(name, startTime)
    })
}

Animation.prototype.playToFinished = function (callback: Function, name?: string) {
    this.off('finished')
    this.once('finished', callback)
    return this.play(name)
}

Animation.prototype.setCompleteListener = function (callback: Function) {
    this.off('finished')
    callback && this.on('finished', callback)
}

Animation.prototype.reset = function (name?: string) {
    name && this.play(name)
    this.setCurrentTime(0)
    this.stop()
}

// Skeleton.prototype.playAsync = function (name: string) {
//     return new Promise<void>(resolve => {
//         this.setCompleteListener(() => {
//             this.isValid && this.setCompleteListener(null)
//             resolve()
//         })
//         this.animation = name
//     })
// }

// Skeleton.prototype.play = function (name: string) {
//     this.animation = name
// }
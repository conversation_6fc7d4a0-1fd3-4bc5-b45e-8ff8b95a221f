import BaseComposite from "./_BaseComposite";
import { BTState } from "./_BTConstant";

// 优先顺序执行，如果一个节点执行成功就返回
export default class Priority extends BaseComposite {

    public onOpen() {
        this.setBlackboardData('startIndex', 0)
    }

    public onTick(dt: number) {
        const startIndex = this.getBlackboardData('startIndex') ?? 0
        for (let i = startIndex, l = this.getChildrenCount(); i < l; i++) {
            let state = this.children[i].execute(dt)
            if (state === BTState.FAILURE) {
                continue
            } else if (state === BTState.RUNNING) {
                this.setBlackboardData('startIndex', i) //这里记录运行中的 下次继续从这里执行
            }
            return state
        }
        return BTState.FAILURE
    }
}

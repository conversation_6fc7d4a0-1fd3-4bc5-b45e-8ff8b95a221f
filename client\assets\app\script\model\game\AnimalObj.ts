import { log } from "cc"
import { AnimalCamp, AnimalState } from "../../common/constant/Enums"
import AnimalStateObj from "./AnimalStateObj"

// 一个动物
export default class AnimalObj {

    public uid: string = ''
    public id: number = 0
    public skinId: number = 0
    public camp: AnimalCamp = AnimalCamp.FRIENDLY //1.我方 2.敌方

    public index: number = 0 //所在位置
    public curHp: number = 0
    public maxHp: number = 0
    public attack: number = 0
    public heal: number = 0 //愈合
    public speed: number = 0 //速度 影响行动顺序
    public attackRange: number = 0 //射程 攻击范围

    public json: any = null
    public state: AnimalStateObj = null //状态

    public init(id: number, camp: AnimalCamp, index: number) {
        this.id = id
        this.uid = id + '_' + ut.UID()
        this.index = index
        this.camp = camp
        this.attack = ut.random(1, 5)
        this.curHp = this.maxHp = ut.random(10, 20)
        return this
    }

    public toFighter() {
        return {
            uid: this.uid,
            camp: this.camp,
            attackIndex: ut.random(0, 9),
        }
    }

    public getViewId() { return this.skinId || this.id }
    public getPrefabUrl() { return 'animal/ANIMAL_' + this.getViewId() }
    public isFriendly() { return this.camp === AnimalCamp.FRIENDLY }

    public isDie() {
        return this.curHp <= 0
    }

    public changeState(state: AnimalState, data?: any) {
        if (!this.state) {
            this.state = new AnimalStateObj()
        }
        this.state.init(state, data)
        // log('changeState', this.uid, this.index, AnimalState[state], data)
    }
}
import BaseBTNode from "./_BaseBTNode";
import { BTType } from "./_BTConstant";

// 组合节点
export default class BaseComposite extends BaseBTNode {

    public children: BaseBTNode[] = []

    constructor() {
        super()
        this.type = BTType.COMPOSITE
    }

    public getChildrenCount() {
        return this.children.length
    }

    // 添加子节点
    public addChild(node: BaseBTNode) {
        if (node) {
            this.children.push(node)
        }
    }
}
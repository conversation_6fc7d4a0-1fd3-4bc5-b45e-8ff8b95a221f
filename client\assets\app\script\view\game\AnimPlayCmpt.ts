import { _decorator, Component, instantiate, Label, Node, Prefab, Vec2 } from "cc";
import { gHelper } from "../../common/helper/GameHelper";

const { ccclass, property } = _decorator;

type FlutterAnimData = {
    node: Node;
    elapsed: number;
    duration: number;
    initX: number;
    initY: number;
}

// 动画播放组建
@ccclass
export default class AnimPlayCmpt extends Component {

    private prefabMap: { [key: string]: Prefab } = {}
    private nodePool: { [key: string]: Node[] } = {}

    // 飘字动物列表
    private flutterAnims: FlutterAnimData[] = []

    public async init(key: string) {
        gHelper.game.addFrameAnimation(this)
        const pfbs = await assetsMgr.loadTempRseDir('flutter', Prefab, key)
        pfbs.forEach(m => this.prefabMap[m.name] = m)
    }

    public clean() {
        gHelper.game.removeFrameAnimation(this.uuid)
        this.prefabMap = {}
        for (let key in this.nodePool) {
            const pool = this.nodePool[key]
            while (pool.length > 0) {
                pool.pop().destroy()
            }
        }
        this.nodePool = {}
        this.destroy()
    }

    // 获取节点
    private getNode(key: string) {
        return this.nodePool[key]?.pop() || instantiate(this.prefabMap[key])
    }
    private putNode(node: Node) {
        let pool = this.nodePool[node.name]
        if (!pool) {
            pool = this.nodePool[node.name] = []
        }
        pool.push(node)
    }

    // 每帧刷新 毫秒
    public updateFrame(dt: number) {
        for (let i = this.flutterAnims.length - 1; i >= 0; i--) {
            const anim = this.flutterAnims[i]
            if (this.doFlutterAnimate(anim, dt)) {
                this.flutterAnims.splice(i, 1)
                this.putNode(anim.node)
            }
        }
    }

    private doFlutterAnimate(anim: FlutterAnimData, dt: number) {
        const node = anim.node
        anim.elapsed += dt
        const progress = Math.min(anim.elapsed / anim.duration, 1)
        // 移动
        // node.x = anim.initX + this.sineOut(progress) * 20
        node.y = anim.initY + this.sineOut(progress) * 80
        // 透明度变化：先快速显现，后缓慢消失
        node.opacity = progress < 0.3 ? 255 : (1 - this.sineIn((progress - 0.3) * 1.43)) * 255
        // 完成
        return progress >= 1
    }

    // sineIn 缓动函数：从0开始加速
    private sineIn(t: number): number {
        return 1 - Math.cos((t * Math.PI) / 2)
    }

    // sineOut 缓动函数：减速到0
    private sineOut(t: number): number {
        return Math.sin((t * Math.PI) / 2)
    }

    // 飘字
    public playFlutter(data: any, root: Node, position: Vec2) {
        if (!root?.isValid) {
            return
        }
        const node = this.getNode('FLUTTER_GENERAL')
        node.parent = root
        const type = data.type, val = data.value
        if (type === 'isMiss') {
            node.Swih('val')[0].Component(Label).Color('#FF9162').string = 'miss'
        } else {
            node.Swih('val')[0].Component(Label).Color(type === 'isHeal' ? '#21DC2D' : '#FF9162').string = '' + Math.abs(val)
        }
        const initX = node.x = position.x + ut.randomRange(-25, 25)
        const initY = node.y = position.y + 20
        node.opacity = 255
        this.flutterAnims.push({ node, elapsed: 0, duration: 1000, initX, initY })
    }
}
import { _decorator, Color, Component, Sprite } from "cc";

const { ccclass, property, menu, requireComponent } = _decorator;

@ccclass
@menu('自定义组件/MultiColor')
@requireComponent(Sprite)
export default class MultiColor extends Component {

    @property([Color])
    private colors: Color[] = []

    private _sprite: Sprite = null!
    private get sprite() { return this._sprite || (this._sprite = this.getComponent(Sprite)!) }

    public setColor(idx: number | boolean) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.colors.length || !this.sprite) {
            return
        }
        this.sprite.color = this.colors[i]
    }
}

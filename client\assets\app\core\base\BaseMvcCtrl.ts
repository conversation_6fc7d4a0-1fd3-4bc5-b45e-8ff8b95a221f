import { Component, Node, Label, Sprite, But<PERSON>, Toggle, Animation, <PERSON>rollView, RichText, ToggleContainer, EditBox, Slider, S<PERSON>eton, js, EventHandler, error } from "cc"

const CmptTypeFix = {
    n: { fix: 'Node_', type: Node },
    l: { fix: 'Lbl_', type: Label },
    s: { fix: 'Spr_', type: Sprite },
    b: { fix: 'Btn_', type: Button },
    t: { fix: 'Tge_', type: Toggle },
    a: { fix: 'Ani_', type: Animation },
    sv: { fix: 'Sv_', type: ScrollView },
    rt: { fix: 'Rt_', type: RichText },
    tc: { fix: 'Tc_', type: ToggleContainer },
    sp: { fix: 'Sp_', type: Skeleton },
    eb: { fix: 'Eb_', type: EditBox },
} as any

const EventTypeFix = {
    be: Button,
    te: Toggle,
    se: Slider,
    tce: ToggleContainer,
    nbe: But<PERSON>,
    ebee: EditBox,
} as any

// 所有控制器的基础类
export default class BaseMvcCtrl extends Component {

    public _isLoadProperty: boolean = false

    // 预加载
    __preload() {
        this.loadProperty()
    }

    // 加载属性
    public loadProperty() {
        if (this._isLoadProperty) {
            return
        }
        // 遍历节点
        const children = this.node.children
        for (let i = 0, l = children.length; i < l; i++) {
            this.__detectionNodeNames(children[i])
        }
        this._isLoadProperty = true
    }

    private __isEvent(val: string) {
        return !!EventTypeFix[val.split('@')[0]]
    }

    private __isVar(val: string) {
        return CmptTypeFix[val] || val === 'w' || val === 'wg'
    }

    private __getHeadVar(arr: string[]) {
        let head = arr[0]
        arr.shift()
        while (arr.length > 0) {
            const val = arr[0]
            if (this.__isVar(val) || this.__isEvent(val)) {
                break
            }
            head += ut.initialUpperCase(val)
            arr.shift()
        }
        return head
    }

    private __detectionNodeNames(node: Node) {
        const arr = node.name.split('_')
        let hasWdt = false, self: any = this
        if (arr.length >= 2 && !!arr[0]) {
            let head = this.__getHeadVar(arr)
            let hasW = !!arr.remove('w')
            for (let i = 0, l = arr.length; i < l; i++) {
                const it = arr[i]
                const ct = CmptTypeFix[it]
                if (ct) {// 组件
                    const vname = head + ct.fix
                    if (self[vname] !== undefined) {
                        if (ct.fix === 'Node_') {
                            self[vname] = node
                        } else {
                            const cmpt = node.getComponent(ct.type)
                            if (cmpt) {
                                self[vname] = cmpt
                            } else {
                                error(vname + ' 没有对应的组件 at=' + js.getClassName(this))
                            }
                        }
                    } else {
                        error(vname + ' 没有找到对应的属性名 at=' + js.getClassName(this))
                    }
                } else if (it === 'wg') {// 挂件
                    const wdt = this.__addWdtComponent(node, ut.initialUpperCase(head) + 'WdtCtrl')
                    if (hasW) {
                        const vname = head + 'Wdt_'
                        if (self[vname] !== undefined) {
                            self[vname] = wdt
                        }
                    }
                    hasWdt = true
                } else {// 事件
                    const [e, data] = it.split('@')
                    const et = EventTypeFix[e]
                    if (!et) {
                    } else if (e === 'nbe') {
                        node.children.forEach(m => this.__addClickEvent(m, head, et, data, false))
                    } else if (e === 'ebee') {
                        this.__addEditBoxEvent(node, head, e, data)
                    } else {
                        this.__addClickEvent(node, head, et, data)
                    }
                }
            }
        }
        if (!hasWdt) {
            const children = node.children
            for (let i = 0, l = children.length; i < l; i++) {
                this.__detectionNodeNames(children[i])
            }
        }
    }

    // 添加EditBox点击事件
    private __addEditBoxEvent(node: Node, head: string, eType: string, data: string) {
        const fname = 'onClick' + ut.initialUpperCase(head) + 'Ended'
        const func = this[fname]
        if (func && typeof (func) === 'function') {
            const cmpt = node.getComponent(EditBox)
            if (cmpt) {
                if (eType === 'ebee') {
                    cmpt.editingDidEnded[0] = this.__newEventHandler(fname, data)
                } else {
                    logger.error(fname + ' 没有对应的events at=' + this['__classname__'] + '.' + node.name)
                }
            } else {
                logger.error(fname + ' 没有对应的组件 at=' + this['__classname__'] + '.' + node.name)
            }
        } else {
            logger.error(fname + ' 没有找到对应的方法名 at=' + this['__classname__'] + '.' + node.name)
        }
    }

    protected __addClickEvent(node: Node, head: string, cmptType: typeof Component, data: string, log: boolean = true) {
        const fname = 'onClick' + ut.initialUpperCase(head)
        const func = this[fname]
        if (func && typeof (func) === 'function') {
            const cmpt = node.getComponent(cmptType)
            if (cmpt) {
                const events = this.__getEvents(cmpt)
                if (events) {
                    events[0] = this.__newEventHandler(fname, data)
                } else {
                    logger.error(fname + ' 没有对应的events at=' + this['__classname__'] + '.' + node.name)
                }
            } else {
                log && logger.error(fname + ' 没有对应的组件 at=' + this['__classname__'] + '.' + node.name)
            }
        } else {
            logger.error(fname + ' 没有找到对应的方法名 at=' + this['__classname__'] + '.' + node.name)
        }
    }

    protected __getEvents(cmpt: Component) {
        if (cmpt instanceof Toggle || cmpt instanceof ToggleContainer) {
            return cmpt.checkEvents
        } else if (cmpt instanceof Button) {
            return cmpt.clickEvents
        } else if (cmpt instanceof Slider) {
            return cmpt.slideEvents
        }
        return null
    }

    protected __newEventHandler(handler: string, data: string) {
        const eventHandler = new EventHandler()
        eventHandler.target = this.node
        eventHandler.component = js.getClassName(this)
        eventHandler.handler = handler
        eventHandler.customEventData = data || ''
        return eventHandler
    }

    private __addWdtComponent(node: Node, className: string) {
        if (!js.getClassByName(className)) {
            error('addWdtComponent error! not found class ' + className)
            return null
        }
        let wdt = node.getComponent(className)
        if (!wdt) {
            wdt = node.addComponent(className)
        }
        return wdt
    }

    protected __wrapListenMaps(listens: any[], out: any[], target: any): any[] {
        listens.forEach(map => {
            let data = { type: '', cb: null, target: target, tag: 'create' }
            for (let key in map) {
                let val = map[key]
                if (typeof (val) === 'function') {
                    data.type = key
                    data.cb = val
                } else if (key === 'enter') {
                    data.tag = key
                }
            }
            out.push(data)
        })
        return out
    }
}
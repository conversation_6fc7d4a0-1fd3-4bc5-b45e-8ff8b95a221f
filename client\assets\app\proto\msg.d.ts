declare global {
 // DO NOT EDIT! This is a generated file. Edit the JSDoc in src/*.js instead and run 'npm run types'.

/** Namespace proto. */
export namespace proto {

    /** Properties of a Vec2. */
    interface IVec2 {

        /** Vec2 x */
        x?: (number|null);

        /** Vec2 y */
        y?: (number|null);
    }

    /** Represents a Vec2. */
    class Vec2 implements IVec2 {

        /**
         * Constructs a new Vec2.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IVec2);

        /** Vec2 x. */
        public x: number;

        /** Vec2 y. */
        public y: number;

        /**
         * Creates a new Vec2 instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Vec2 instance
         */
        public static create(properties?: proto.IVec2): proto.Vec2;

        /**
         * Encodes the specified Vec2 message. Does not implicitly {@link proto.Vec2.verify|verify} messages.
         * @param message Vec2 message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IVec2, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Vec2 message, length delimited. Does not implicitly {@link proto.Vec2.verify|verify} messages.
         * @param message Vec2 message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IVec2, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Vec2 message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Vec2
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.Vec2;

        /**
         * Decodes a Vec2 message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Vec2
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.Vec2;

        /**
         * Verifies a Vec2 message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Vec2 message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Vec2
         */
        public static fromObject(object: { [k: string]: any }): proto.Vec2;

        /**
         * Creates a plain object from a Vec2 message. Also converts values to other types if specified.
         * @param message Vec2
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.Vec2, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Vec2 to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an OnUpdatePlayerInfoNotify. */
    interface IOnUpdatePlayerInfoNotify {

        /** OnUpdatePlayerInfoNotify type */
        type?: (number|null);
    }

    /** Represents an OnUpdatePlayerInfoNotify. */
    class OnUpdatePlayerInfoNotify implements IOnUpdatePlayerInfoNotify {

        /**
         * Constructs a new OnUpdatePlayerInfoNotify.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IOnUpdatePlayerInfoNotify);

        /** OnUpdatePlayerInfoNotify type. */
        public type: number;

        /**
         * Creates a new OnUpdatePlayerInfoNotify instance using the specified properties.
         * @param [properties] Properties to set
         * @returns OnUpdatePlayerInfoNotify instance
         */
        public static create(properties?: proto.IOnUpdatePlayerInfoNotify): proto.OnUpdatePlayerInfoNotify;

        /**
         * Encodes the specified OnUpdatePlayerInfoNotify message. Does not implicitly {@link proto.OnUpdatePlayerInfoNotify.verify|verify} messages.
         * @param message OnUpdatePlayerInfoNotify message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IOnUpdatePlayerInfoNotify, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified OnUpdatePlayerInfoNotify message, length delimited. Does not implicitly {@link proto.OnUpdatePlayerInfoNotify.verify|verify} messages.
         * @param message OnUpdatePlayerInfoNotify message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IOnUpdatePlayerInfoNotify, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an OnUpdatePlayerInfoNotify message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns OnUpdatePlayerInfoNotify
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.OnUpdatePlayerInfoNotify;

        /**
         * Decodes an OnUpdatePlayerInfoNotify message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns OnUpdatePlayerInfoNotify
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.OnUpdatePlayerInfoNotify;

        /**
         * Verifies an OnUpdatePlayerInfoNotify message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an OnUpdatePlayerInfoNotify message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns OnUpdatePlayerInfoNotify
         */
        public static fromObject(object: { [k: string]: any }): proto.OnUpdatePlayerInfoNotify;

        /**
         * Creates a plain object from an OnUpdatePlayerInfoNotify message. Also converts values to other types if specified.
         * @param message OnUpdatePlayerInfoNotify
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.OnUpdatePlayerInfoNotify, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this OnUpdatePlayerInfoNotify to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a UserInfo. */
    interface IUserInfo {

        /** UserInfo uid */
        uid?: (string|null);

        /** UserInfo loginType */
        loginType?: (string|null);

        /** UserInfo totalGameCount */
        totalGameCount?: (number[]|null);

        /** UserInfo loginDayCount */
        loginDayCount?: (number|null);

        /** UserInfo nickname */
        nickname?: (string|null);

        /** UserInfo createTime */
        createTime?: (number|Long|null);

        /** UserInfo sumOnlineTime */
        sumOnlineTime?: (number|Long|null);

        /** UserInfo sessionId */
        sessionId?: (string|null);

        /** UserInfo roleId */
        roleId?: (number|null);
    }

    /** Represents a UserInfo. */
    class UserInfo implements IUserInfo {

        /**
         * Constructs a new UserInfo.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IUserInfo);

        /** UserInfo uid. */
        public uid: string;

        /** UserInfo loginType. */
        public loginType: string;

        /** UserInfo totalGameCount. */
        public totalGameCount: number[];

        /** UserInfo loginDayCount. */
        public loginDayCount: number;

        /** UserInfo nickname. */
        public nickname: string;

        /** UserInfo createTime. */
        public createTime: (number|Long);

        /** UserInfo sumOnlineTime. */
        public sumOnlineTime: (number|Long);

        /** UserInfo sessionId. */
        public sessionId: string;

        /** UserInfo roleId. */
        public roleId: number;

        /**
         * Creates a new UserInfo instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UserInfo instance
         */
        public static create(properties?: proto.IUserInfo): proto.UserInfo;

        /**
         * Encodes the specified UserInfo message. Does not implicitly {@link proto.UserInfo.verify|verify} messages.
         * @param message UserInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IUserInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UserInfo message, length delimited. Does not implicitly {@link proto.UserInfo.verify|verify} messages.
         * @param message UserInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IUserInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UserInfo message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UserInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.UserInfo;

        /**
         * Decodes a UserInfo message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UserInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.UserInfo;

        /**
         * Verifies a UserInfo message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UserInfo message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UserInfo
         */
        public static fromObject(object: { [k: string]: any }): proto.UserInfo;

        /**
         * Creates a plain object from a UserInfo message. Also converts values to other types if specified.
         * @param message UserInfo
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.UserInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UserInfo to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an Item. */
    interface IItem {

        /** Item id */
        id?: (number|null);

        /** Item lv */
        lv?: (number|null);
    }

    /** Represents an Item. */
    class Item implements IItem {

        /**
         * Constructs a new Item.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IItem);

        /** Item id. */
        public id: number;

        /** Item lv. */
        public lv: number;

        /**
         * Creates a new Item instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Item instance
         */
        public static create(properties?: proto.IItem): proto.Item;

        /**
         * Encodes the specified Item message. Does not implicitly {@link proto.Item.verify|verify} messages.
         * @param message Item message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IItem, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Item message, length delimited. Does not implicitly {@link proto.Item.verify|verify} messages.
         * @param message Item message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IItem, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Item message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Item
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.Item;

        /**
         * Decodes an Item message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Item
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.Item;

        /**
         * Verifies an Item message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Item message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Item
         */
        public static fromObject(object: { [k: string]: any }): proto.Item;

        /**
         * Creates a plain object from an Item message. Also converts values to other types if specified.
         * @param message Item
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.Item, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Item to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an Animal. */
    interface IAnimal {

        /** Animal id */
        id?: (number|null);

        /** Animal lv */
        lv?: (number|null);
    }

    /** Represents an Animal. */
    class Animal implements IAnimal {

        /**
         * Constructs a new Animal.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IAnimal);

        /** Animal id. */
        public id: number;

        /** Animal lv. */
        public lv: number;

        /**
         * Creates a new Animal instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Animal instance
         */
        public static create(properties?: proto.IAnimal): proto.Animal;

        /**
         * Encodes the specified Animal message. Does not implicitly {@link proto.Animal.verify|verify} messages.
         * @param message Animal message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IAnimal, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Animal message, length delimited. Does not implicitly {@link proto.Animal.verify|verify} messages.
         * @param message Animal message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IAnimal, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Animal message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Animal
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.Animal;

        /**
         * Decodes an Animal message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Animal
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.Animal;

        /**
         * Verifies an Animal message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Animal message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Animal
         */
        public static fromObject(object: { [k: string]: any }): proto.Animal;

        /**
         * Creates a plain object from an Animal message. Also converts values to other types if specified.
         * @param message Animal
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.Animal, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Animal to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a ShopInfo. */
    interface IShopInfo {

        /** ShopInfo Id */
        Id?: (number|null);

        /** ShopInfo items */
        items?: (proto.IItem[]|null);
    }

    /** Represents a ShopInfo. */
    class ShopInfo implements IShopInfo {

        /**
         * Constructs a new ShopInfo.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IShopInfo);

        /** ShopInfo Id. */
        public Id: number;

        /** ShopInfo items. */
        public items: proto.IItem[];

        /**
         * Creates a new ShopInfo instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ShopInfo instance
         */
        public static create(properties?: proto.IShopInfo): proto.ShopInfo;

        /**
         * Encodes the specified ShopInfo message. Does not implicitly {@link proto.ShopInfo.verify|verify} messages.
         * @param message ShopInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IShopInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ShopInfo message, length delimited. Does not implicitly {@link proto.ShopInfo.verify|verify} messages.
         * @param message ShopInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IShopInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ShopInfo message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ShopInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.ShopInfo;

        /**
         * Decodes a ShopInfo message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ShopInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.ShopInfo;

        /**
         * Verifies a ShopInfo message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ShopInfo message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ShopInfo
         */
        public static fromObject(object: { [k: string]: any }): proto.ShopInfo;

        /**
         * Creates a plain object from a ShopInfo message. Also converts values to other types if specified.
         * @param message ShopInfo
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.ShopInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ShopInfo to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a PlayerInfo. */
    interface IPlayerInfo {

        /** PlayerInfo uid */
        uid?: (string|null);

        /** PlayerInfo nickname */
        nickname?: (string|null);

        /** PlayerInfo roleId */
        roleId?: (number|null);

        /** PlayerInfo day */
        day?: (number|null);

        /** PlayerInfo hp */
        hp?: (number[]|null);

        /** PlayerInfo winCount */
        winCount?: (number|null);

        /** PlayerInfo animals */
        animals?: (proto.IAnimal[]|null);

        /** PlayerInfo bags */
        bags?: (proto.IItem[]|null);
    }

    /** Represents a PlayerInfo. */
    class PlayerInfo implements IPlayerInfo {

        /**
         * Constructs a new PlayerInfo.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IPlayerInfo);

        /** PlayerInfo uid. */
        public uid: string;

        /** PlayerInfo nickname. */
        public nickname: string;

        /** PlayerInfo roleId. */
        public roleId: number;

        /** PlayerInfo day. */
        public day: number;

        /** PlayerInfo hp. */
        public hp: number[];

        /** PlayerInfo winCount. */
        public winCount: number;

        /** PlayerInfo animals. */
        public animals: proto.IAnimal[];

        /** PlayerInfo bags. */
        public bags: proto.IItem[];

        /**
         * Creates a new PlayerInfo instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerInfo instance
         */
        public static create(properties?: proto.IPlayerInfo): proto.PlayerInfo;

        /**
         * Encodes the specified PlayerInfo message. Does not implicitly {@link proto.PlayerInfo.verify|verify} messages.
         * @param message PlayerInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IPlayerInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerInfo message, length delimited. Does not implicitly {@link proto.PlayerInfo.verify|verify} messages.
         * @param message PlayerInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IPlayerInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerInfo message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.PlayerInfo;

        /**
         * Decodes a PlayerInfo message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.PlayerInfo;

        /**
         * Verifies a PlayerInfo message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerInfo message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerInfo
         */
        public static fromObject(object: { [k: string]: any }): proto.PlayerInfo;

        /**
         * Creates a plain object from a PlayerInfo message. Also converts values to other types if specified.
         * @param message PlayerInfo
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.PlayerInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerInfo to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a GameData. */
    interface IGameData {

        /** GameData player */
        player?: (proto.IPlayerInfo|null);

        /** GameData otherPlayer */
        otherPlayer?: (proto.IPlayerInfo|null);

        /** GameData shop */
        shop?: (proto.IShopInfo|null);
    }

    /** Represents a GameData. */
    class GameData implements IGameData {

        /**
         * Constructs a new GameData.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IGameData);

        /** GameData player. */
        public player?: (proto.IPlayerInfo|null);

        /** GameData otherPlayer. */
        public otherPlayer?: (proto.IPlayerInfo|null);

        /** GameData shop. */
        public shop?: (proto.IShopInfo|null);

        /**
         * Creates a new GameData instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GameData instance
         */
        public static create(properties?: proto.IGameData): proto.GameData;

        /**
         * Encodes the specified GameData message. Does not implicitly {@link proto.GameData.verify|verify} messages.
         * @param message GameData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IGameData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GameData message, length delimited. Does not implicitly {@link proto.GameData.verify|verify} messages.
         * @param message GameData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IGameData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GameData message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GameData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.GameData;

        /**
         * Decodes a GameData message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GameData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.GameData;

        /**
         * Verifies a GameData message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GameData message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GameData
         */
        public static fromObject(object: { [k: string]: any }): proto.GameData;

        /**
         * Creates a plain object from a GameData message. Also converts values to other types if specified.
         * @param message GameData
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.GameData, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GameData to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a S2C_RESULT. */
    interface IS2C_RESULT {

        /** S2C_RESULT data */
        data?: (Uint8Array|null);

        /** S2C_RESULT error */
        error?: (string|null);
    }

    /** Represents a S2C_RESULT. */
    class S2C_RESULT implements IS2C_RESULT {

        /**
         * Constructs a new S2C_RESULT.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IS2C_RESULT);

        /** S2C_RESULT data. */
        public data: Uint8Array;

        /** S2C_RESULT error. */
        public error: string;

        /**
         * Creates a new S2C_RESULT instance using the specified properties.
         * @param [properties] Properties to set
         * @returns S2C_RESULT instance
         */
        public static create(properties?: proto.IS2C_RESULT): proto.S2C_RESULT;

        /**
         * Encodes the specified S2C_RESULT message. Does not implicitly {@link proto.S2C_RESULT.verify|verify} messages.
         * @param message S2C_RESULT message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IS2C_RESULT, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified S2C_RESULT message, length delimited. Does not implicitly {@link proto.S2C_RESULT.verify|verify} messages.
         * @param message S2C_RESULT message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IS2C_RESULT, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a S2C_RESULT message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns S2C_RESULT
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.S2C_RESULT;

        /**
         * Decodes a S2C_RESULT message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns S2C_RESULT
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.S2C_RESULT;

        /**
         * Verifies a S2C_RESULT message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a S2C_RESULT message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns S2C_RESULT
         */
        public static fromObject(object: { [k: string]: any }): proto.S2C_RESULT;

        /**
         * Creates a plain object from a S2C_RESULT message. Also converts values to other types if specified.
         * @param message S2C_RESULT
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.S2C_RESULT, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this S2C_RESULT to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LOGIN_HD_GUESTLOGIN_C2S. */
    interface ILOGIN_HD_GUESTLOGIN_C2S {

        /** LOGIN_HD_GUESTLOGIN_C2S guestId */
        guestId?: (string|null);

        /** LOGIN_HD_GUESTLOGIN_C2S nickname */
        nickname?: (string|null);

        /** LOGIN_HD_GUESTLOGIN_C2S platform */
        platform?: (string|null);
    }

    /** Represents a LOGIN_HD_GUESTLOGIN_C2S. */
    class LOGIN_HD_GUESTLOGIN_C2S implements ILOGIN_HD_GUESTLOGIN_C2S {

        /**
         * Constructs a new LOGIN_HD_GUESTLOGIN_C2S.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.ILOGIN_HD_GUESTLOGIN_C2S);

        /** LOGIN_HD_GUESTLOGIN_C2S guestId. */
        public guestId: string;

        /** LOGIN_HD_GUESTLOGIN_C2S nickname. */
        public nickname: string;

        /** LOGIN_HD_GUESTLOGIN_C2S platform. */
        public platform: string;

        /**
         * Creates a new LOGIN_HD_GUESTLOGIN_C2S instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LOGIN_HD_GUESTLOGIN_C2S instance
         */
        public static create(properties?: proto.ILOGIN_HD_GUESTLOGIN_C2S): proto.LOGIN_HD_GUESTLOGIN_C2S;

        /**
         * Encodes the specified LOGIN_HD_GUESTLOGIN_C2S message. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_C2S.verify|verify} messages.
         * @param message LOGIN_HD_GUESTLOGIN_C2S message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.ILOGIN_HD_GUESTLOGIN_C2S, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LOGIN_HD_GUESTLOGIN_C2S message, length delimited. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_C2S.verify|verify} messages.
         * @param message LOGIN_HD_GUESTLOGIN_C2S message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.ILOGIN_HD_GUESTLOGIN_C2S, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LOGIN_HD_GUESTLOGIN_C2S message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LOGIN_HD_GUESTLOGIN_C2S
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.LOGIN_HD_GUESTLOGIN_C2S;

        /**
         * Decodes a LOGIN_HD_GUESTLOGIN_C2S message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LOGIN_HD_GUESTLOGIN_C2S
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.LOGIN_HD_GUESTLOGIN_C2S;

        /**
         * Verifies a LOGIN_HD_GUESTLOGIN_C2S message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LOGIN_HD_GUESTLOGIN_C2S message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LOGIN_HD_GUESTLOGIN_C2S
         */
        public static fromObject(object: { [k: string]: any }): proto.LOGIN_HD_GUESTLOGIN_C2S;

        /**
         * Creates a plain object from a LOGIN_HD_GUESTLOGIN_C2S message. Also converts values to other types if specified.
         * @param message LOGIN_HD_GUESTLOGIN_C2S
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.LOGIN_HD_GUESTLOGIN_C2S, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LOGIN_HD_GUESTLOGIN_C2S to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LOGIN_HD_GUESTLOGIN_S2C. */
    interface ILOGIN_HD_GUESTLOGIN_S2C {

        /** LOGIN_HD_GUESTLOGIN_S2C accountToken */
        accountToken?: (string|null);

        /** LOGIN_HD_GUESTLOGIN_S2C guestId */
        guestId?: (string|null);
    }

    /** Represents a LOGIN_HD_GUESTLOGIN_S2C. */
    class LOGIN_HD_GUESTLOGIN_S2C implements ILOGIN_HD_GUESTLOGIN_S2C {

        /**
         * Constructs a new LOGIN_HD_GUESTLOGIN_S2C.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.ILOGIN_HD_GUESTLOGIN_S2C);

        /** LOGIN_HD_GUESTLOGIN_S2C accountToken. */
        public accountToken: string;

        /** LOGIN_HD_GUESTLOGIN_S2C guestId. */
        public guestId: string;

        /**
         * Creates a new LOGIN_HD_GUESTLOGIN_S2C instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LOGIN_HD_GUESTLOGIN_S2C instance
         */
        public static create(properties?: proto.ILOGIN_HD_GUESTLOGIN_S2C): proto.LOGIN_HD_GUESTLOGIN_S2C;

        /**
         * Encodes the specified LOGIN_HD_GUESTLOGIN_S2C message. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_S2C.verify|verify} messages.
         * @param message LOGIN_HD_GUESTLOGIN_S2C message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.ILOGIN_HD_GUESTLOGIN_S2C, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LOGIN_HD_GUESTLOGIN_S2C message, length delimited. Does not implicitly {@link proto.LOGIN_HD_GUESTLOGIN_S2C.verify|verify} messages.
         * @param message LOGIN_HD_GUESTLOGIN_S2C message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.ILOGIN_HD_GUESTLOGIN_S2C, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LOGIN_HD_GUESTLOGIN_S2C message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LOGIN_HD_GUESTLOGIN_S2C
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.LOGIN_HD_GUESTLOGIN_S2C;

        /**
         * Decodes a LOGIN_HD_GUESTLOGIN_S2C message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LOGIN_HD_GUESTLOGIN_S2C
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.LOGIN_HD_GUESTLOGIN_S2C;

        /**
         * Verifies a LOGIN_HD_GUESTLOGIN_S2C message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LOGIN_HD_GUESTLOGIN_S2C message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LOGIN_HD_GUESTLOGIN_S2C
         */
        public static fromObject(object: { [k: string]: any }): proto.LOGIN_HD_GUESTLOGIN_S2C;

        /**
         * Creates a plain object from a LOGIN_HD_GUESTLOGIN_S2C message. Also converts values to other types if specified.
         * @param message LOGIN_HD_GUESTLOGIN_S2C
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.LOGIN_HD_GUESTLOGIN_S2C, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LOGIN_HD_GUESTLOGIN_S2C to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LOBBY_HD_TRYLOGIN_C2S. */
    interface ILOBBY_HD_TRYLOGIN_C2S {

        /** LOBBY_HD_TRYLOGIN_C2S accountToken */
        accountToken?: (string|null);

        /** LOBBY_HD_TRYLOGIN_C2S lang */
        lang?: (string|null);

        /** LOBBY_HD_TRYLOGIN_C2S platform */
        platform?: (string|null);

        /** LOBBY_HD_TRYLOGIN_C2S version */
        version?: (string|null);
    }

    /** Represents a LOBBY_HD_TRYLOGIN_C2S. */
    class LOBBY_HD_TRYLOGIN_C2S implements ILOBBY_HD_TRYLOGIN_C2S {

        /**
         * Constructs a new LOBBY_HD_TRYLOGIN_C2S.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.ILOBBY_HD_TRYLOGIN_C2S);

        /** LOBBY_HD_TRYLOGIN_C2S accountToken. */
        public accountToken: string;

        /** LOBBY_HD_TRYLOGIN_C2S lang. */
        public lang: string;

        /** LOBBY_HD_TRYLOGIN_C2S platform. */
        public platform: string;

        /** LOBBY_HD_TRYLOGIN_C2S version. */
        public version: string;

        /**
         * Creates a new LOBBY_HD_TRYLOGIN_C2S instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LOBBY_HD_TRYLOGIN_C2S instance
         */
        public static create(properties?: proto.ILOBBY_HD_TRYLOGIN_C2S): proto.LOBBY_HD_TRYLOGIN_C2S;

        /**
         * Encodes the specified LOBBY_HD_TRYLOGIN_C2S message. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_C2S.verify|verify} messages.
         * @param message LOBBY_HD_TRYLOGIN_C2S message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.ILOBBY_HD_TRYLOGIN_C2S, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LOBBY_HD_TRYLOGIN_C2S message, length delimited. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_C2S.verify|verify} messages.
         * @param message LOBBY_HD_TRYLOGIN_C2S message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.ILOBBY_HD_TRYLOGIN_C2S, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LOBBY_HD_TRYLOGIN_C2S message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LOBBY_HD_TRYLOGIN_C2S
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.LOBBY_HD_TRYLOGIN_C2S;

        /**
         * Decodes a LOBBY_HD_TRYLOGIN_C2S message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LOBBY_HD_TRYLOGIN_C2S
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.LOBBY_HD_TRYLOGIN_C2S;

        /**
         * Verifies a LOBBY_HD_TRYLOGIN_C2S message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LOBBY_HD_TRYLOGIN_C2S message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LOBBY_HD_TRYLOGIN_C2S
         */
        public static fromObject(object: { [k: string]: any }): proto.LOBBY_HD_TRYLOGIN_C2S;

        /**
         * Creates a plain object from a LOBBY_HD_TRYLOGIN_C2S message. Also converts values to other types if specified.
         * @param message LOBBY_HD_TRYLOGIN_C2S
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.LOBBY_HD_TRYLOGIN_C2S, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LOBBY_HD_TRYLOGIN_C2S to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LOBBY_HD_TRYLOGIN_S2C. */
    interface ILOBBY_HD_TRYLOGIN_S2C {

        /** LOBBY_HD_TRYLOGIN_S2C user */
        user?: (proto.IUserInfo|null);

        /** LOBBY_HD_TRYLOGIN_S2C accountToken */
        accountToken?: (string|null);

        /** LOBBY_HD_TRYLOGIN_S2C banAccountType */
        banAccountType?: (number|null);

        /** LOBBY_HD_TRYLOGIN_S2C banAccountSurplusTime */
        banAccountSurplusTime?: (number|Long|null);

        /** LOBBY_HD_TRYLOGIN_S2C gameBaseData */
        gameBaseData?: (proto.IPlayerInfo|null);
    }

    /** Represents a LOBBY_HD_TRYLOGIN_S2C. */
    class LOBBY_HD_TRYLOGIN_S2C implements ILOBBY_HD_TRYLOGIN_S2C {

        /**
         * Constructs a new LOBBY_HD_TRYLOGIN_S2C.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.ILOBBY_HD_TRYLOGIN_S2C);

        /** LOBBY_HD_TRYLOGIN_S2C user. */
        public user?: (proto.IUserInfo|null);

        /** LOBBY_HD_TRYLOGIN_S2C accountToken. */
        public accountToken: string;

        /** LOBBY_HD_TRYLOGIN_S2C banAccountType. */
        public banAccountType: number;

        /** LOBBY_HD_TRYLOGIN_S2C banAccountSurplusTime. */
        public banAccountSurplusTime: (number|Long);

        /** LOBBY_HD_TRYLOGIN_S2C gameBaseData. */
        public gameBaseData?: (proto.IPlayerInfo|null);

        /**
         * Creates a new LOBBY_HD_TRYLOGIN_S2C instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LOBBY_HD_TRYLOGIN_S2C instance
         */
        public static create(properties?: proto.ILOBBY_HD_TRYLOGIN_S2C): proto.LOBBY_HD_TRYLOGIN_S2C;

        /**
         * Encodes the specified LOBBY_HD_TRYLOGIN_S2C message. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_S2C.verify|verify} messages.
         * @param message LOBBY_HD_TRYLOGIN_S2C message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.ILOBBY_HD_TRYLOGIN_S2C, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LOBBY_HD_TRYLOGIN_S2C message, length delimited. Does not implicitly {@link proto.LOBBY_HD_TRYLOGIN_S2C.verify|verify} messages.
         * @param message LOBBY_HD_TRYLOGIN_S2C message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.ILOBBY_HD_TRYLOGIN_S2C, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LOBBY_HD_TRYLOGIN_S2C message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LOBBY_HD_TRYLOGIN_S2C
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.LOBBY_HD_TRYLOGIN_S2C;

        /**
         * Decodes a LOBBY_HD_TRYLOGIN_S2C message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LOBBY_HD_TRYLOGIN_S2C
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.LOBBY_HD_TRYLOGIN_S2C;

        /**
         * Verifies a LOBBY_HD_TRYLOGIN_S2C message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LOBBY_HD_TRYLOGIN_S2C message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LOBBY_HD_TRYLOGIN_S2C
         */
        public static fromObject(object: { [k: string]: any }): proto.LOBBY_HD_TRYLOGIN_S2C;

        /**
         * Creates a plain object from a LOBBY_HD_TRYLOGIN_S2C message. Also converts values to other types if specified.
         * @param message LOBBY_HD_TRYLOGIN_S2C
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.LOBBY_HD_TRYLOGIN_S2C, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LOBBY_HD_TRYLOGIN_S2C to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a GAME_HD_ENTRY_C2S. */
    interface IGAME_HD_ENTRY_C2S {

        /** GAME_HD_ENTRY_C2S nickname */
        nickname?: (string|null);

        /** GAME_HD_ENTRY_C2S roleId */
        roleId?: (number|null);
    }

    /** Represents a GAME_HD_ENTRY_C2S. */
    class GAME_HD_ENTRY_C2S implements IGAME_HD_ENTRY_C2S {

        /**
         * Constructs a new GAME_HD_ENTRY_C2S.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IGAME_HD_ENTRY_C2S);

        /** GAME_HD_ENTRY_C2S nickname. */
        public nickname: string;

        /** GAME_HD_ENTRY_C2S roleId. */
        public roleId: number;

        /**
         * Creates a new GAME_HD_ENTRY_C2S instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GAME_HD_ENTRY_C2S instance
         */
        public static create(properties?: proto.IGAME_HD_ENTRY_C2S): proto.GAME_HD_ENTRY_C2S;

        /**
         * Encodes the specified GAME_HD_ENTRY_C2S message. Does not implicitly {@link proto.GAME_HD_ENTRY_C2S.verify|verify} messages.
         * @param message GAME_HD_ENTRY_C2S message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IGAME_HD_ENTRY_C2S, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GAME_HD_ENTRY_C2S message, length delimited. Does not implicitly {@link proto.GAME_HD_ENTRY_C2S.verify|verify} messages.
         * @param message GAME_HD_ENTRY_C2S message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IGAME_HD_ENTRY_C2S, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GAME_HD_ENTRY_C2S message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GAME_HD_ENTRY_C2S
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.GAME_HD_ENTRY_C2S;

        /**
         * Decodes a GAME_HD_ENTRY_C2S message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GAME_HD_ENTRY_C2S
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.GAME_HD_ENTRY_C2S;

        /**
         * Verifies a GAME_HD_ENTRY_C2S message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GAME_HD_ENTRY_C2S message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GAME_HD_ENTRY_C2S
         */
        public static fromObject(object: { [k: string]: any }): proto.GAME_HD_ENTRY_C2S;

        /**
         * Creates a plain object from a GAME_HD_ENTRY_C2S message. Also converts values to other types if specified.
         * @param message GAME_HD_ENTRY_C2S
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.GAME_HD_ENTRY_C2S, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GAME_HD_ENTRY_C2S to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a GAME_HD_ENTRY_S2C. */
    interface IGAME_HD_ENTRY_S2C {

        /** GAME_HD_ENTRY_S2C gameData */
        gameData?: (proto.IGameData|null);
    }

    /** Represents a GAME_HD_ENTRY_S2C. */
    class GAME_HD_ENTRY_S2C implements IGAME_HD_ENTRY_S2C {

        /**
         * Constructs a new GAME_HD_ENTRY_S2C.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IGAME_HD_ENTRY_S2C);

        /** GAME_HD_ENTRY_S2C gameData. */
        public gameData?: (proto.IGameData|null);

        /**
         * Creates a new GAME_HD_ENTRY_S2C instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GAME_HD_ENTRY_S2C instance
         */
        public static create(properties?: proto.IGAME_HD_ENTRY_S2C): proto.GAME_HD_ENTRY_S2C;

        /**
         * Encodes the specified GAME_HD_ENTRY_S2C message. Does not implicitly {@link proto.GAME_HD_ENTRY_S2C.verify|verify} messages.
         * @param message GAME_HD_ENTRY_S2C message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IGAME_HD_ENTRY_S2C, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GAME_HD_ENTRY_S2C message, length delimited. Does not implicitly {@link proto.GAME_HD_ENTRY_S2C.verify|verify} messages.
         * @param message GAME_HD_ENTRY_S2C message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IGAME_HD_ENTRY_S2C, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GAME_HD_ENTRY_S2C message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GAME_HD_ENTRY_S2C
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.GAME_HD_ENTRY_S2C;

        /**
         * Decodes a GAME_HD_ENTRY_S2C message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GAME_HD_ENTRY_S2C
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.GAME_HD_ENTRY_S2C;

        /**
         * Verifies a GAME_HD_ENTRY_S2C message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GAME_HD_ENTRY_S2C message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GAME_HD_ENTRY_S2C
         */
        public static fromObject(object: { [k: string]: any }): proto.GAME_HD_ENTRY_S2C;

        /**
         * Creates a plain object from a GAME_HD_ENTRY_S2C message. Also converts values to other types if specified.
         * @param message GAME_HD_ENTRY_S2C
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.GAME_HD_ENTRY_S2C, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GAME_HD_ENTRY_S2C to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LOBBY_ONUPDATEUSERINFO_NOTIFY. */
    interface ILOBBY_ONUPDATEUSERINFO_NOTIFY {

        /** LOBBY_ONUPDATEUSERINFO_NOTIFY list */
        list?: (proto.IOnUpdatePlayerInfoNotify[]|null);
    }

    /** Represents a LOBBY_ONUPDATEUSERINFO_NOTIFY. */
    class LOBBY_ONUPDATEUSERINFO_NOTIFY implements ILOBBY_ONUPDATEUSERINFO_NOTIFY {

        /**
         * Constructs a new LOBBY_ONUPDATEUSERINFO_NOTIFY.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY);

        /** LOBBY_ONUPDATEUSERINFO_NOTIFY list. */
        public list: proto.IOnUpdatePlayerInfoNotify[];

        /**
         * Creates a new LOBBY_ONUPDATEUSERINFO_NOTIFY instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LOBBY_ONUPDATEUSERINFO_NOTIFY instance
         */
        public static create(properties?: proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY): proto.LOBBY_ONUPDATEUSERINFO_NOTIFY;

        /**
         * Encodes the specified LOBBY_ONUPDATEUSERINFO_NOTIFY message. Does not implicitly {@link proto.LOBBY_ONUPDATEUSERINFO_NOTIFY.verify|verify} messages.
         * @param message LOBBY_ONUPDATEUSERINFO_NOTIFY message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LOBBY_ONUPDATEUSERINFO_NOTIFY message, length delimited. Does not implicitly {@link proto.LOBBY_ONUPDATEUSERINFO_NOTIFY.verify|verify} messages.
         * @param message LOBBY_ONUPDATEUSERINFO_NOTIFY message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.ILOBBY_ONUPDATEUSERINFO_NOTIFY, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LOBBY_ONUPDATEUSERINFO_NOTIFY message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LOBBY_ONUPDATEUSERINFO_NOTIFY
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.LOBBY_ONUPDATEUSERINFO_NOTIFY;

        /**
         * Decodes a LOBBY_ONUPDATEUSERINFO_NOTIFY message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LOBBY_ONUPDATEUSERINFO_NOTIFY
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.LOBBY_ONUPDATEUSERINFO_NOTIFY;

        /**
         * Verifies a LOBBY_ONUPDATEUSERINFO_NOTIFY message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LOBBY_ONUPDATEUSERINFO_NOTIFY message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LOBBY_ONUPDATEUSERINFO_NOTIFY
         */
        public static fromObject(object: { [k: string]: any }): proto.LOBBY_ONUPDATEUSERINFO_NOTIFY;

        /**
         * Creates a plain object from a LOBBY_ONUPDATEUSERINFO_NOTIFY message. Also converts values to other types if specified.
         * @param message LOBBY_ONUPDATEUSERINFO_NOTIFY
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.LOBBY_ONUPDATEUSERINFO_NOTIFY, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LOBBY_ONUPDATEUSERINFO_NOTIFY to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a GAME_ONKICK_NOTIFY. */
    interface IGAME_ONKICK_NOTIFY {

        /** GAME_ONKICK_NOTIFY type */
        type?: (number|null);

        /** GAME_ONKICK_NOTIFY banType */
        banType?: (number|null);
    }

    /** Represents a GAME_ONKICK_NOTIFY. */
    class GAME_ONKICK_NOTIFY implements IGAME_ONKICK_NOTIFY {

        /**
         * Constructs a new GAME_ONKICK_NOTIFY.
         * @param [properties] Properties to set
         */
        constructor(properties?: proto.IGAME_ONKICK_NOTIFY);

        /** GAME_ONKICK_NOTIFY type. */
        public type: number;

        /** GAME_ONKICK_NOTIFY banType. */
        public banType: number;

        /**
         * Creates a new GAME_ONKICK_NOTIFY instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GAME_ONKICK_NOTIFY instance
         */
        public static create(properties?: proto.IGAME_ONKICK_NOTIFY): proto.GAME_ONKICK_NOTIFY;

        /**
         * Encodes the specified GAME_ONKICK_NOTIFY message. Does not implicitly {@link proto.GAME_ONKICK_NOTIFY.verify|verify} messages.
         * @param message GAME_ONKICK_NOTIFY message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: proto.IGAME_ONKICK_NOTIFY, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GAME_ONKICK_NOTIFY message, length delimited. Does not implicitly {@link proto.GAME_ONKICK_NOTIFY.verify|verify} messages.
         * @param message GAME_ONKICK_NOTIFY message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: proto.IGAME_ONKICK_NOTIFY, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GAME_ONKICK_NOTIFY message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GAME_ONKICK_NOTIFY
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): proto.GAME_ONKICK_NOTIFY;

        /**
         * Decodes a GAME_ONKICK_NOTIFY message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GAME_ONKICK_NOTIFY
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): proto.GAME_ONKICK_NOTIFY;

        /**
         * Verifies a GAME_ONKICK_NOTIFY message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GAME_ONKICK_NOTIFY message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GAME_ONKICK_NOTIFY
         */
        public static fromObject(object: { [k: string]: any }): proto.GAME_ONKICK_NOTIFY;

        /**
         * Creates a plain object from a GAME_ONKICK_NOTIFY message. Also converts values to other types if specified.
         * @param message GAME_ONKICK_NOTIFY
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: proto.GAME_ONKICK_NOTIFY, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GAME_ONKICK_NOTIFY to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }
}
 
} 
 export {proto}
syntax = "proto3";

package proto;

option go_package = "./pb";

//-----------------------------数据结构------------------------------------

// 二维坐标数据
message Vec2 {
  int32 x = 1; //x轴
  int32 y = 2; //y轴
}

// int32数组
message Int32ArrayInfo { 
  repeated int32 arr = 1;
}

// 玩家数据
message UserInfo {
  string uid                              = 1; //uid
  string loginType                        = 2; //登录方式
  repeated int32 totalGameCount           = 3; //总局数
  int32 loginDayCount                     = 4; //登录天数
  string nickname                         = 5; //昵称
  int64 createTime                        = 6; //创建时间
  int64 sumOnlineTime                     = 7; //累计在线时间
  string sessionId                        = 8; //每次登录唯一id
  int32 roleId                            = 9; //当前使用的角色id
}

// 物品
message Item {
  int32 id = 1;
  int32 lv = 2; //等级
  repeated Int32ArrayInfo attrs = 3; //属性
}

// 商店信息
message ShopInfo {
	int32 Id            = 1; //商店id
  repeated Item items = 2; //物品列表
  Item selectItem     = 3; //选择的物品
}

// 玩家信息
message PlayerInfo {
  string uid              = 1;
  string nickname         = 2;
	int32 roleId            = 3; //角色id
	int32 day               = 4; //天数
  repeated int32 hp       = 5; //血量
  int32 winCount          = 6; //胜利次数
  repeated Item animals   = 7; //动物列表
  repeated Item bags      = 8; //背包
  int32 gold              = 9; //金币
  int32 earnings          = 10; //收益
}

// 游戏数据
message GameData {
  PlayerInfo player       = 1; //其他玩家信息
  PlayerInfo otherPlayer  = 2; //其他玩家信息
  ShopInfo shop           = 3; //商店信息
}

// 地图数据
message MapData {
  repeated Int32ArrayInfo maps  = 1; //地图数据
  repeated int32 paths          = 2; //路径数据
}

//-----------------------------协议------------------------------------

// 服务器返回协议
message S2C_RESULT {
  bytes data    = 1;
  string error  = 2;
}

// 游客登录
message LOGIN_HD_GUESTLOGIN_C2S {
  string guestId    = 1; //游客id
  string nickname   = 2; //昵称
  string platform   = 3; //平台
}
// 游客登录返回
message LOGIN_HD_GUESTLOGIN_S2C {
  string accountToken = 1; //登录token
  string guestId      = 2; //游客id
}

// 尝试登录
message LOBBY_HD_TRYLOGIN_C2S {
  string accountToken   = 1; //登录token
  string lang           = 2; //语言
  string platform       = 3; //平台
  string version        = 4; //版本
}
// 尝试登录 返回
message LOBBY_HD_TRYLOGIN_S2C {
  UserInfo user       = 1;  //玩家信息
  string accountToken = 2;  //登录token
  int32 banAccountType        = 3; //封禁类型
  int64 banAccountSurplusTime = 4; //封禁剩余时间
  PlayerInfo gameBaseData     = 5; //游戏基础数据
}

// 进入游戏
message GAME_HD_ENTRY_C2S {
  string nickname         = 1;
	int32 roleId            = 2; //角色id
}
message GAME_HD_ENTRY_S2C {
  MapData mapData   = 1; //地图数据
  GameData gameData = 2; //游戏数据
}

// 商店选择
message GAME_HD_SHOPSELECT_C2S {
  int32 index = 1; //选择的物品索引
}
message GAME_HD_SHOPSELECT_S2C {
  GameData gameData = 1; //游戏数据
}

//-----------------------------服务器主动推送------------------------------------

// 用户数据更新通知
message LOBBY_ONUPDATEUSERINFO_NOTIFY {
  repeated OnUpdatePlayerInfoNotify list = 1; //玩家数据更新通知列表
}

// 玩家数据更新通知数据
message OnUpdatePlayerInfoNotify {
  int32 type = 1; //类型
}

// 主动踢下线通知
message GAME_ONKICK_NOTIFY {
  int32 type    = 1;
  int32 banType = 2;
}
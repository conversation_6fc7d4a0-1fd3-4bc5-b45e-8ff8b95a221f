import { _decorator, Component, Label, Node } from "cc";
import AnimalObj from "../../model/game/AnimalObj";

const { ccclass, property } = _decorator;

// 属性条
@ccclass
export default class AttrBarCmpt extends Component {

    private main: Node = null
    private data: AnimalObj = null

    public init(data: AnimalObj) {
        this.data = data
        this.main = this.Child('main')
        this.reset()
        return this
    }

    public clean() {
        this.node.destroy()
        this.data = null
    }

    // 初始化信息
    public reset() {
        if (!this.data) {
            return
        }
        this.node.active = !this.data.isDie()
        this.main.Child('1/val', mc.LabelRollNumber).set(this.data.curHp)
        this.main.Child('2/val', mc.LabelRollNumber).set(this.data.attack)
    }

    // 同步信息
    public play() {
        if (!this.data) {
            return
        }
        this.main.Child('1/val', mc.LabelRollNumber).to(this.data.curHp)
        this.main.Child('2/val', mc.LabelRollNumber).to(this.data.attack)
    }
}
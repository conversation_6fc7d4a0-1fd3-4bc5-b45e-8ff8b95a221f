import { instantiate, js, Node, Prefab, sys } from "cc";
import BaseWindCtrl from "../base/BaseWindCtrl"
import CoreEventType from "../event/CoreEventType"
import { loader } from "../utils/ResLoader"

type ProgressPercentCallback = (percent: number) => void;

export default class WindCtrlMgr {

    public node: Node = null

    public currWind: BaseWindCtrl = null
    private nextWind: BaseWindCtrl = null
    private caches: Map<string, Node> = new Map<string, Node>()

    private isLockGoto: boolean = false

    // 清理缓存中的wind
    public cleanCacheWind() {
        this.caches.forEach((node, key) => {
            const wind: BaseWindCtrl = node.getComponent<BaseWindCtrl>(`${ut.initialUpperCase(key)}WindCtrl`)
            wind.__clean()
            node.destroy()
            // 释放临时资源
            assetsMgr.releaseTempResByTag(key)
            // 最后释放场景
            loader.releaseRes(`view/${key}/${ut.initialUpperCase(key)}Wind`, Prefab)
        })
        this.caches.clear()
    }

    // 加载wind
    private async load(key: string, progress?: ProcessCallback) {
        const pfb: Prefab = await loader.loadRes(`view/${key}/${ut.initialUpperCase(key)}Wind`, Prefab, progress)
        if (!pfb) {
            return logger.error('load scene error!')
        }
        return this.ready(instantiate(pfb), key)
    }

    private putWind(wind: BaseWindCtrl) {
        // 释放音效
        // audioMgr.releaseByMod(wind.key)
        // 释放所有pnl
        eventCenter.emit(CoreEventType.CLOSE_ALL_PNL)
        // 先调用离开
        wind.__leave()
        // 释放场景
        if (wind.isClean) {
            wind.__clean()
            wind.node.destroy()
            // 释放临时资源
            assetsMgr.releaseTempResByTag(wind.key)
            // 最后释放场景
            loader.releaseRes(`view/${wind.key}/${ut.initialUpperCase(wind.key)}Wind`, Prefab)
        } else {
            wind.node.parent = null
            this.caches.set(wind.key, wind.node)
        }
    }

    // 准备场景
    private async ready(it: Node, key: string) {
        const className = `${ut.initialUpperCase(key)}WindCtrl`
        if (!js.getClassByName(className)) {
            return logger.error('load wind error! not found class ' + className)
        }
        let wind: BaseWindCtrl = it.getComponent<BaseWindCtrl>(className) || it.addComponent<BaseWindCtrl>(className)
        if (!wind || !(wind instanceof BaseWindCtrl)) {
            return logger.error('load wind error! not found class ' + className)
        }
        it.parent = this.node
        this.nextWind = wind
        this.nextWind.key = key
        this.nextWind.node.zIndex = 0
        // 这里检查一下 是否还没有加载属性
        if (!wind._isLoadProperty) {
            logger.error('load wind error! not load property. at=' + className)
            wind.loadProperty()
        }
        wind.setActive(false)
        // 如果是从缓存里面取的那就不初始化了
        if (!this.caches.delete(key)) {
            await wind.__create()
        }
        // 进入前的准备 每次进入场景都会调用
        await wind.__ready()
    }

    // 显示场景
    private show(...params: any) {
        if (!this.nextWind) {
            return
        }
        // 关闭当前场景
        this.currWind && this.putWind(this.currWind)
        // gc
        ut.waitNextFrame(2).then(() => {
            if (typeof wx !== 'undefined') {
                wx.triggerGC()
            } else if (sys.isNative) {
                sys.garbageCollect()
            }
        })
        // 进入下个场景
        const prevKey = this.currWind?.key || ''
        this.currWind = this.nextWind
        this.nextWind = null
        this.currWind.setActive(true)
        this.currWind.node.zIndex = 10
        this.currWind.__enter(...params)
        // 发送进入场景事件
        eventCenter.emit(CoreEventType.WIND_ENTER, this.currWind, prevKey)
    }

    // 预加载场景
    public async preLoad(key: string, progress: ProgressPercentCallback, complete: Function) {
        if (this.currWind?.key === key || this.nextWind?.key === key) {
            progress && progress(1)
            complete && complete()
            return // 如果已经有了直接返回
        }
        let _done: number = 0, _total: number = 1
        let it = this.caches.get(key) //是否有缓存
        if (it) {
            await this.ready(it, key)
        } else {
            await this.load(key, (done, total) => {
                _done = done
                _total = total + 1
                progress && progress(_done / _total)
            })
        }
        _done = _total
        progress && progress(1)
        complete && complete()
    }

    // 加载一个场景
    public async goto(key: string, ...params: any) {
        if (!key || this.isLockGoto) {
            return
        } else if (this.currWind?.key === key) { //是否已经打开了
            return this.currWind.__enter(...params)
        } else if (this.nextWind?.key === key) { //是否已经预加载了
            return this.show(...params)
        }
        this.isLockGoto = true
        mc.lockTouch('__goto_wind__')
        let it = this.caches.get(key) //是否有缓存
        if (it) {
            eventCenter.emit(CoreEventType.READY_BEGIN_WIND)
            await this.ready(it, key)
            eventCenter.emit(CoreEventType.READY_END_WIND)
        } else {
            eventCenter.emit(CoreEventType.LOAD_BEGIN_WIND, key)
            await this.load(key)
            eventCenter.emit(CoreEventType.LOAD_END_WIND, key)
        }
        mc.unlockTouch('__goto_wind__')
        this.isLockGoto = false
        // 显示出来
        this.show(...params)
    }
}
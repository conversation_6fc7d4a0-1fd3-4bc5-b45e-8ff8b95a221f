import { log } from "cc";
import Fighter from "./FSPFighter";
import { BEHAVIOR_CONFIG, BEHAVIOR_FIRST_ID, BTType } from "./_BTConstant";
import BaseBTNode from "./_BaseBTNode";
import { bevTreeFactory } from "./_BevTreeFactory";
import BaseDecorator from "./_BaseDecorator";
import BaseComposite from "./_BaseComposite";
import { BehaviorNodeConfInfo } from "../../common/constant/DataType";

// 行为树
export default class BehaviorTree {

    private root: BaseBTNode = null

    public load(fighter: Fighter) {
        this.root = this.loadNode(BEHAVIOR_FIRST_ID, fighter, 0)
        // 打印
        // this.printNode(this.root, 0)
        return this
    }

    private loadNode(id: number, fighter: Fighter, index: number) {
        const json: BehaviorNodeConfInfo = BEHAVIOR_CONFIG[id]
        const node = bevTreeFactory.newNode(json?.cls)
        if (!node) {
            logger.error('BehaviorTree load config error, cls=' + json?.cls)
            return null
        }
        node.init(id, fighter, index)
        // 添加子节点
        json.children.forEach((m, i) => node.addChild(this.loadNode(m, fighter, i)))
        return node
    }

    public tick(dt: number) {
        if (!this.root) {
            return
        }
        const state = this.root.execute(dt)
    }

    public isCanRun() { return !!this.root }

    // 打印节点出来
    private printNode(root: BaseBTNode, blk: number) {
        let str = ''
        for (let i = 0; i < blk; i++) {
            str += ' ' //缩进
        }
        log(str + '|—' + BEHAVIOR_CONFIG[root.id].cls)
        if (root.type == BTType.DECORATOR) {
            let dec = root as BaseDecorator
            if (dec.child) {
                return this.printNode(dec.child, blk + 3)
            }
        } else if (root.type === BTType.COMPOSITE) {
            let comp = root as BaseComposite
            for (let i = 0, l = comp.getChildrenCount(); i < l; i++) {
                this.printNode(comp.children[i], blk + 3)
            }
        }
    }
}
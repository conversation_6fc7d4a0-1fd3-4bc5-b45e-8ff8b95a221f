import { instantiate, Node, Prefab, Tween } from "cc";

type NodePoolItem = {
    prefab: Prefab;
    items: Node[];
    itemsUuidMap: any;
    keys: string[];
}

// 对象池管理
class NodePoolMgr {

    private readonly LOAD_KEY = '__node_pool_load_key'

    private debug: boolean = false
    private nodePoolMap: Map<string, NodePoolItem> = new Map()
    private useItems: { uuid: string, key: string, node: Node }[] = [] //当前正在使用的node

    public async get(url: string, key?: string) {
        key = key ?? '__none'
        let pool = this.nodePoolMap.get(url)
        if (!pool || !pool.prefab || !pool.prefab.isValid) {
            const prefab = await assetsMgr.loadTempRes(url, Prefab, this.LOAD_KEY)
            if (!prefab) {
                logger.error('nodePool get prefab is null, url=' + url)
                return null
            }
            pool = this.nodePoolMap.get(url) //防止异步回来的时候 已经有了
            if (!pool) {
                pool = { prefab: prefab, items: [], itemsUuidMap: {}, keys: [] }
                this.nodePoolMap.set(url, pool)
            }
        }
        if (!pool.keys.has(key)) {
            pool.keys.push(key)
        }
        let node = pool.items.pop()
        delete pool.itemsUuidMap[node?.uuid]
        if (!node || !node.isValid) {
            node = instantiate(pool.prefab)
        }
        if (node) {
            node['__@node_pool_url'] = url
            node['__@node_pool_key'] = key
            this.useItems.push({ uuid: node.uuid, key: key, node: node })
        }
        if (this.debug) {
            logger.info('nodePool get ' + url + ', <' + key + '> ' + !!node)
        }
        return node
    }

    public put(node: Node) {
        if (!node || !node.isValid) {
            return
        }
        const url = node['__@node_pool_url']
        if (!url) {
            node.destroy()
            return logger.error('nodePool put error! url == null, node=' + node.name)
        }
        const pool = this.nodePoolMap.get(url)
        if (!pool || pool.items.length >= 100) {
            node.destroy()
            return logger.error('nodePool put error! not pool, node=' + node.name)
        } else if (!pool.itemsUuidMap[node.uuid]) {
            node.stopAllActions()
            node.parent = null
            node.Data = null
            pool.items.push(node)
            pool.itemsUuidMap[node.uuid] = true
            this.useItems.remove('uuid', node.uuid)
            if (this.debug) {
                logger.info('nodePool put ' + url + ', <' + node['__@node_pool_key'] + '> ')
            }
        } else {
            logger.info('nodePool put error! repeat push item, node=' + node.name)
        }
    }

    private releaseOne(url: string, pool: NodePoolItem) {
        pool.items.forEach(m => m.destroy())
        pool.items.length = 0
        pool.prefab?.destroy()
        assetsMgr.releaseTempRes(url, this.LOAD_KEY)
        this.nodePoolMap.delete(url)
        if (this.debug) {
            logger.info('nodePool release ' + url)
        }
    }

    // 释放所有
    public releaseAll() {
        this.nodePoolMap.forEach((m, url) => this.releaseOne(url, m))
    }

    // 释放
    public releaseByTag(key: string) {
        this.nodePoolMap.forEach((m, url) => {
            if (m.keys.remove(key) !== undefined) {
                // this.removeItemsOne(m, key)
                if (m.keys.length === 0) {
                    this.releaseOne(url, m)
                }
            }
        })
    }

    // 释放
    public releaseByUrl(url: string) {
        const pool = this.nodePoolMap.get(url)
        if (pool) {
            this.releaseOne(url, pool)
        }
    }

    // 清理items
    public removeItemsByTag(key: string) {
        this.nodePoolMap.forEach(m => this.removeItemsOne(m, key))
    }

    private removeItemsOne(pool: NodePoolItem, key: string) {
        if (!pool.keys.remove(key)) {
            return
        }
        for (let i = pool.items.length - 1; i >= 0; i--) {
            const node = pool.items[i]
            if (node['__@node_pool_key'] === key) {
                pool.items.splice(i, 1)
                delete pool.itemsUuidMap[node?.uuid]
                node.destroy()
            }
        }
    }

    // 清理使用中的
    public cleanUseItemsByTag(key: string) {
        for (let i = this.useItems.length - 1; i >= 0; i--) {
            const data = this.useItems[i], isValid = data.node?.isValid
            if (data.key !== key && isValid) {
                continue
            } else if (isValid) {
                data.node.destroy()
            }
            this.useItems.splice(i, 1)
        }
    }

    public cleanUseAndRemoveItemsByTag(key: string) {
        this.cleanUseItemsByTag(key)
        this.removeItemsByTag(key)
    }
}

window['nodePoolMgr'] = new NodePoolMgr()
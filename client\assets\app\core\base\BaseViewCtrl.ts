import { Component } from "cc"
import BaseMvcCtrl from "./BaseMvcCtrl"

// 基础视图控制器
export default class BaseViewCtrl extends BaseMvcCtrl {

    public _state: string = 'none'
    private __listens: any[] = [] //监听列表

    public __register(val?: string) {
        this.__listens.forEach(({ type, cb, target, tag }) => (!val || val === tag) && eventCenter.on(type, cb, target))
    }

    public __unregister(val?: string) {
        this.__listens.forEach(({ type, cb, target, tag }) => (!val || val === tag) && eventCenter.off(type, cb, target))
    }

    public __listenMaps() {
        this.__wrapListenMaps(this.listenEventMaps(), this.__listens, this)
    }

    public addListener(type: string, cb: Function, target?: any) {
        this.__listens.push({ type: type, cb: cb, target: target })
        if (this._state === 'enter') {
            eventCenter.on(type, cb, target)
        }
    }

    public removeListener(type: string) {
        const data = this.__listens.remove('type', type)
        if (data) {
            eventCenter.off(data.type, data.cb, data.target)
        }
    }

    public emit(type: string | number, ...params: any) {
        eventCenter.emit(type, ...params)
    }

    // 添加点击事件
    public addClickEvent(cmpt: Component, handler: string, data?: string) {
        if (!cmpt) {
            return
        }
        if (!this[handler] || typeof (this[handler]) !== 'function') {
            return logger.error(handler + ' 没有找到对应的方法名 at=' + this['__classname__'])
        }
        const events = this.__getEvents(cmpt)
        if (events) {
            events[0] = this.__newEventHandler(handler, data)
        } else {
            return logger.error(handler + ' 没有对应的events at=' + cmpt.name)
        }
    }

    public listenEventMaps(): any[] {
        return []
    }

    public getModel<T>(key: string): T {
        return mc.getModel(key)
    }
}
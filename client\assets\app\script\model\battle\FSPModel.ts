import { log } from "cc";
import FSPController from "./FSPController";
import { FSP_PLAY_MUL } from "../../common/constant/Constant";
import GameModel from "../game/GameModel";

// 帧同步 一场战斗对应一个
export default class FSPModel {

    public isRunning: boolean = false

    private game: GameModel = null
    private fspCtrl: FSPController = null
    private isLocal: boolean = false //是否本地战斗
    private fps: number = 0 //帧率
    private mul: number = 0 //加速倍数
    private dt: number = 0
    private currentFrameIndex: number = 0 //当前帧
    private serverFrameIndex: number = 0 //服务器当前的帧数

    private initInterval: number = 0
    private fpsInterval: number = 0
    private fpsElapsed: number = 0
    private speedMulIndex: number = 2 //当前加速倍数下标
    private pause: boolean = false //是否暂停
    private startTime: number = 0

    constructor() {
        this.game = GameModel.ins()
    }

    public init(data: any) {
        this.fps = data.fps ?? 40
        this.mul = data.mul || 1
        this.currentFrameIndex = data.currentFrameIndex ?? 0
        this.fpsInterval = this.initInterval = this.fpsElapsed = 1 / this.fps
        this.dt = Math.floor(1000 / this.fps)
        this.fspCtrl = new FSPController().init(data)
        this.isRunning = true
        this.pause = false
        this.startTime = Date.now()
        return this
    }

    public stop() {
        this.isRunning = false
        this.pause = false
        this.fspCtrl?.stop()
        this.fspCtrl = null
        log('fsp stop ' + ((Date.now() - this.startTime) * 0.001) + 's, frameCount: ' + this.currentFrameIndex)
    }

    public getCurrentFrameIndex() { return this.currentFrameIndex }
    public getBattleTime() { return this.currentFrameIndex * this.dt } //获取战斗时间
    public getFspController() { return this.fspCtrl }
    public getMul() { return this.mul }
    // 暂停
    public setPause(val: boolean) { this.pause = val }
    public isPause() { return this.pause }
    // 设置播放倍速
    public getSpeedMulIndex() { return this.speedMulIndex }
    public setSpeedMulIndex(index: number) {
        this.speedMulIndex = index
        this.fpsInterval = this.initInterval * (FSP_PLAY_MUL[index]?.val || 1)
        this.fpsElapsed = 0
    }

    // 刷新
    public update(dt: number) {
        if (!this.isRunning || this.pause) {
            return
        }
        this.fpsElapsed += dt
        if (this.fpsElapsed >= this.fpsInterval) {
            if (dt >= this.fpsInterval) {
                this.fpsElapsed = 0
            } else {
                this.fpsElapsed -= this.fpsInterval
            }
            for (let i = 0; i < this.mul; i++) {
                this.tick()
                this.serverFrameIndex += 1 //模拟服务器的帧
            }
            // // 加速 回放不加速
            // if (!this.isLocal && this.currentFrameIndex >= this.currUpSpeedFrame && this.mul < 3) {
            //     this.mul += 1
            //     this.currUpSpeedFrame += this.upSpeedFrame * this.mul
            //     eventCenter.emit(EventType.UPDATE_AREA_BATTLE_TIME_UI, this.area.index)
            // }
        }
    }

    // 服务器下发的当前帧信息
    public onFSPCheckFrame(data: any) {

    }

    // 每帧刷新
    private tick() {
        // // 获取速度
        // let speed = this.getFrameSpeed(frameDiff)
        // // 执行
        // while (speed > 0) {
        //     speed -= 1
        //     this.executeOneFrame()
        // }
        this.executeOneFrame()
    }

    // 后台计算一帧
    public executeOneFrameForSimulate() {
        this.currentFrameIndex += 1
        this.fspCtrl?.updateFrame(this.dt)
    }

    // 执行一帧
    private executeOneFrame() {
        this.currentFrameIndex += 1
        this.fspCtrl?.updateFrame(this.dt)
        this.game.updateAnimationFrame(this.dt)
    }

    public setCheckHasFrameData(cb: Function) {
        // this.onCheckHasFrameData = cb
    }

    // 检测是否有帧数据
    private checkHasFrameData(currentFrameIndex: number) {
        // const arr = this.otherFrameDataMap[currentFrameIndex]
        // if (!arr || !this.fspCtrl) {
        //     return
        // } else if (!this.isLocal) { //本地数据的话 就不要删除因为下次还要用
        //     delete this.otherFrameDataMap[currentFrameIndex]
        // }
        // arr.forEach(data => this.checkHasFrameDataItem(data))
    }

    public checkHasFrameDataItem(data: any) {

    }

    // 获取帧速度
    private getFrameSpeed(frameNum: number): number {
        let speed = 1
        if (frameNum > 100) {
            speed = 8
        } else if (frameNum > 50) {
            speed = 4
        } else if (frameNum > 4) {
            speed = 2
        }
        return speed
    }
}
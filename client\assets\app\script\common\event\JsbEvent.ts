/**
 * 原生事件
 */
export default {
    COPY_TO_CLIPBOARD: 'COPY_TO_CLIPBOARD', //拷贝内容到剪贴板
    GET_PACKAGE_SIGN: 'GET_PACKAGE_SIGN', //获取包签名

    REWARD_AD_ON_ERROR: 'REWARD_AD_ON_ERROR', //监听广告错误信息
    LOAD_REWARD_VIDEO_AD: 'LOAD_REWARD_VIDEO_AD', //加载广告
    SHOW_REWARD_VIDEO_AD: 'SHOW_REWARD_VIDEO_AD', //播放广告
    CLOSE_NATIVE_AD: 'CLOSE_NATIVE_AD', //关闭广告

    FACEBOOK_LOGIN: 'FACEBOOK_LOGIN', //facebook登陆
    FACEBOOK_LOGOUT: 'FACEBOOK_LOGOUT', //facebook注销
    APPLE_LOGIN: 'APPLE_LOGIN', //苹果登陆
    GOOGLE_LOGIN: 'GOOGLE_LOGIN', //google登陆
    GOOGLE_LOGOUT: 'GOOGLE_LOGOUT', // google注销

    FIREBASE_REGISTER: 'FIREBASE_REGISTER', //在ios用户使用谷歌登录之后 如果玩家的账号是新号 首次登录 那需要调用
    FIREBASE_LOGIN: 'FIREBASE_LOGIN', //ios用户(谷歌登录)每次进游戏的时候 要调用
    FIREBASE_SIGNOUT: 'FIREBASE_SIGNOUT', //我们切换账号的时候不是还会调用各种登出么 ios用户(谷歌登录)切换账号的时候 除了要调用注销 还要多调用一个
    FIREBASE_UPLOAD_EMAIL: 'FIREBASE_UPLOAD_EMAIL',//上报email 但是不需要传参数

    /*
    * @returns {Android}
        {"result":[{"currency_pay":"HKD","currency_price":"8000000","price":"HK$8.00","productId":"jwm_ingot_1"},{"currency_pay":"HKD","currency_price":"39900000","price":"HK$39.90","productId":"jwm_ingot_2"}],"subs":[{"currency_pay":"HKD","currency_price":"15000000","planId":"jwm-ad-free-week","price":"HK$15.00","productId":"jwm_ad_free","token":"xxxx"},{"currency_pay":"HKD","currency_price":"55900000","planId":"jwm-ad-free-month","price":"HK$55.90","productId":"jwm_ad_free","token":"xxxx"}]}
      @returns {ios}  
        {"result":"jwm_ingot_1,¥8,CNY,8|jwm_ingot_3,¥68,CNY,68|jwmAdFreeWeek,¥15,CNY,15,3,¥3|jwm_gold_2,¥38,CNY,38|jwm_ingot_6,¥698,CNY,698|jwm_gold_4,¥148,CNY,148|jwm_ingot_5,¥328,CNY,328|jwm_gold_3,¥68,CNY,68|jwm_ingot_4,¥148,CNY,148|jwmAdFreeMonth,¥48,CNY,48,15,¥15|jwm_ingot_2,¥38,CNY,38|jwm_gold_1,¥8,CNY,8"}
    **/
    IAP_INIT: 'IAP_INIT', //初始化 商品配置

    /*
    * @returns 
       {"result":{"acknowledged":false,"obfuscatedAccountId":"7036608909641125888_79926507","orderId":"GPA.xxx-xxxx-xxxx-xxxx","packageName":"twgame.global.acers","productId":"jwm_ingot_2","purchaseState":0,"purchaseTime":*************,"purchaseToken":"xxxx","quantity":1}}  
       {"result":{"acknowledged":false,"autoRenewing":true,"obfuscatedAccountId":"7036608908860985344_79926507","orderId":"GPA.xxx-xxxx-xxxx-xxxx","packageName":"twgame.global.acers","productId":"jwm_ad_free","purchaseState":0,"purchaseTime":*************,"purchaseToken":"xxxx","quantity":1}}
    **/
    GOOGLE_PAY: 'GOOGLE_PAY', //拉起google支付
    APPLE_PAY: 'APPLE_PAY', //拉起apple支付
    GET_LOST_ORDER_LIST: 'GET_LOST_ORDER_LIST', //获取未完成的订单 因为返回值可能过大所以使用函数getLangOrderList调用
    CONSUME_ORDER: 'CONSUME_ORDER', //标记订单完成

    FACEBOOK_SHARE_PHOTO: 'FACEBOOK_SHARE_PHOTO', //facebook分享图片
    FACEBOOK_SHARE_LINK: 'FACEBOOK_SHARE_LINK', //facebook分享链接

    GET_INSTALL_PARAMS: 'GET_INSTALL_PARAMS', //获取安装参数

    REQUEST_PERMISSION: 'REQUEST_PERMISSION', //请求相关权限
    OPEN_APP_SETTING: 'OPEN_APP_SETTING', //打开app权限设置界面

    FACEBOOK_EVENT: 'FACEBOOK_EVENT', //facebook 事件上报 目前只有海外有
    FIREBASE_EVENT: 'FIREBASE_EVENT', //firebase 事件上报 目前只有海外有
    APPFLYER_EVENT: 'APPFLYER_EVENT', //appflyer 事件上报 目前只有海外有

    SAVE_DEVICE_DATA: 'SAVE_DEVICE_DATA', //将一些数据存在设备中，即使app被卸载数据也存在 目前只有ios有
    GET_DEVICE_DATA: "GET_DEVICE_DATA", // 获取存在设备中的数据
    DEL_DEVICE_DATA: "DEL_DEVICE_DATA", // 删除存在设备中的数据

    GET_NOTICE_TOKEN: "GET_NOTICE_TOKEN", // 获取通知的令牌
    CHECK_NOTICE_PER: "CHECK_NOTICE_PER", // 检测是否有通知的权限

    APPSFLYER_TE: "APPSFLYER_TE", // 将数数的id传给af 达成数据互通
    START_APPSFLYER: "START_APPSFLYER", //  启动af(同时传数数的游客id) 所以现在af必须得跟数数一起启动

    RESTORED_SUB: "RESTORED_SUB", //订阅恢复 不能和GET_LOST_ORDER_LIST同时使用 因为返回值可能过大所以使用函数getLangOrderList调用

    IS_LIMITED_FB_LOGIN: "IS_LIMITED_FB_LOGIN", //粗略判断在ATT没有授权情况下 为受限登录
    GET_DEVICE_INFO: "GET_DEVICE_INFO", //获取设备信息

    GET_AD_INFO: 'GET_AD_INFO', //获取广告信息
    INIT_AD: "INIT_AD", //初始化广告
}
import Fighter from "./FSPFighter"
import { BTState, BTType } from "./_BTConstant"

// 基础节点
export default class BaseBTNode {

    public id: number = 0
    public index: number = 0
    public type: BTType = BTType.NONE
    public fighter: Fighter = null //目标

    public key: number = 0 //唯一标识

    public init(id: number, fighter: Fighter, index: number) {
        this.id = id
        this.index = index
        this.fighter = fighter
        this.key = this.id * 100 + index
        this.onInit()
    }

    public execute(dt: number) {
        // cc.log('node execute [' + this.json.cls + ']' + this.index)
        if (!this.getBlackboardData('isOpen')) {
            this.open()
        }
        this.enter()
        const state = this.onTick(dt)
        this.leave(state)
        return state
    }

    private open() {
        this.setBlackboardData('isOpen', true)
        this.onOpen()
    }

    private enter() {
        this.onEnter()
    }

    private leave(state: BTState) {
        this.onLeave(state)
    }

    public onInit() { }
    public onOpen() { } //第一次打开这个节点
    public onEnter() { }
    public onTick(dt: number) { return BTState.ERROR }
    public onLeave(state: BTState) { }
    public onClean() { } //清理
    public addChild(node: BaseBTNode) { } //添加子节点

    public get ctrl() {
        return this.fighter.ctrl
    }

    // 交互数据
    public get blackboard() {
        return this.fighter.getBlackboard(this.key)
    }

    public setBlackboardData(key: string, val: any) {
        this.blackboard[key] = val
    }

    public getBlackboardData(key: string) {
        return this.blackboard[key]
    }

    public removeBlackboardData(key: string) {
        delete this.blackboard[key]
    }

    // 全局的交互数据
    public getTreeBlackboardData(key: string) {
        return this.fighter.getBlackboard(0)[key]
    }

    public setTreeBlackboardData(key: string, val: any) {
        this.fighter.getBlackboard(0)[key] = val
    }
}
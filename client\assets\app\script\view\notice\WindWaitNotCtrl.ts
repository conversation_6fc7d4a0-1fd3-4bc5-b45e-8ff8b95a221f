import { _decorator, log } from "cc";
const { ccclass } = _decorator;

@ccclass
export default class WindWaitNotCtrl extends mc.BaseNoticeCtrl {

    //@autocode property begin
    //@end

    public listenEventMaps() {
        return [
            { [mc.Event.LOAD_BEGIN_WIND]: this.onEventOpen },
            { [mc.Event.LOAD_END_WIND]: this.onEventHide },
            { [mc.Event.READY_BEGIN_WIND]: this.onEventOpen },
            { [mc.Event.READY_END_WIND]: this.onEventHide },
        ]
    }

    public async onCreate() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onEventOpen() {
        this.open()
    }

    private onEventHide() {
        this.hide()
    }
    // ----------------------------------------- custom function ----------------------------------------------------

}

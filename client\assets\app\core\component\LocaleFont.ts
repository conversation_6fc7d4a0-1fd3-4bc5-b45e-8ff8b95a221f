import { _decorator, CCString, Label } from "cc";
import Base<PERSON>ocale from "../base/BaseLocale";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu('多语言组件/LocaleFont')
export default class LocaleFont extends BaseLocale {

    @property(CCString)
    private fontName: string = ''

    private _label: Label = null

    private _lang: string = ''
    private _font: string = ''

    private _change: boolean = false

    private get label() {
        if (!this._label) {
            this._label = this.Component(Label)
        }
        return this._label
    }

    onEnable() {
        if (!mc.lang) {
            return
        } else if (this._lang !== mc.lang) {
            this._lang = mc.lang
            this._font = ''
        } else if (this.label.font && !this.label.font.isValid) {
            this._font = ''
        }
        if (!this._font) {
            this.updateFont()
        }
        this._change = mc.canChangeLang
        if (this._change) {
            eventCenter.on(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    onDisable() {
        if (this._change) {
            this._change = false
            eventCenter.off(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    // 语言切换
    private onLanguageChanged(lang: string) {
        this._lang = lang
        this._font = ''
        this.updateFont()
    }

    public set string(val: string) {
        this.label.string = val
    }
    public get string() { return this.label.string }

    public updateLang() {
        this._lang = mc.lang
    }

    // 刷新字体
    private updateFont() {
        if (this.fontName !== '' && this.fontName !== this._font) {
            this.setFont(this.fontName)
        }
    }

    public setFont(fontUrl: string) {
        if (!this.label) {
            return
        }
        this._font = fontUrl
        // const text = this.label.string
        // this.label.string = ''
        const font = assetsMgr.getFont(fontUrl)
        if (font) {
            this.label.font = font
        } else {
            this.label.font = null
            this._font = ''
        }
        // this.label.string = text
    }
}

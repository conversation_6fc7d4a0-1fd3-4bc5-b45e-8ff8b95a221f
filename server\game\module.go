package game

import (
	"casrv/server/common/config"
	"casrv/server/common/ecode"
	"casrv/server/common/pb"
	ut "casrv/utils"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Game)
}

type Game struct {
	basemodule.BaseModule
}

func (this *Game) GetType() string {
	return "game"
}

func (this *Game) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Game) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Game) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	// id := settings.ID
	// this.BaseModule.OnInit(this, app, settings, server.ID(id))
	// this.GetServer().Options().Metadata["sid"] = strings.Replace(id, "game", "", 1) //设置元数据 方便路由的时候区分节点
	this.InitRpc()

	this.GetServer().RegisterGO("HD_Entry", this.entry)           //进入游戏
	this.GetServer().RegisterGO("HD_ShopSelect", this.shopSelect) //商店选择
}

func (this *Game) Run(closeSig chan bool) {
	// r.RunAllRoom(&this.BaseModule)
	// GetRoom(1).Run()
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	// r.SaveAllRoom()
}

func (this *Game) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 进入游戏
func (this *Game) entry(session gate.Session, msg *pb.GAME_HD_ENTRY_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	// 先获取信息
	data := GetGameData(uid)
	if data == nil {
		data = CreateGame(uid, msg.GetNickname(), msg.GetRoleId())
		log.Info("create game uid: %v, day: %v", uid, data.Player.Day)
	} else {
		log.Info("entry game uid: %v, day: %v", uid, data.Player.Day)
	}
	return pb.ProtoMarshal(&pb.GAME_HD_ENTRY_S2C{GameData: data.ToPb()})
}

// 商店选择
func (this *Game) shopSelect(session gate.Session, msg *pb.GAME_HD_SHOPSELECT_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	data := GetGameData(uid)
	if data == nil {
		log.Error("shopSelect error, not gameData! uid: %v", uid)
		return nil, ecode.UNKNOWN.String()
	} else if data.Shop == nil {
		log.Error("shopSelect error, not shopInfo! uid: %v", uid)
		return nil, ecode.UNKNOWN.String()
	}
	shop := data.Shop
	json := config.GetJsonData("shop", shop.Id)
	if json == nil {
		log.Error("shopSelect error, not shop json! uid: %v, shopId: %v", uid, shop.Id)
		return nil, ecode.UNKNOWN.String()
	}
	s2c := &pb.GAME_HD_SHOPSELECT_S2C{}
	tp, index := ut.Int32(json["type"]), msg.GetIndex()
	switch tp {
	case SHOP_TYPE_BEGIN: //开局选择
		switch index {
		case 0: //随机变异动物
			211001
		case 1: //金币和收益
		case 2: //随机遗物
		}
	default:
	}
	shop.State = 1
	// 最后保存游戏数据
	SaveGameData(uid, data)
	return pb.ProtoMarshal(s2c)
}

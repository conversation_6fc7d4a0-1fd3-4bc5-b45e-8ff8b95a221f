import BTAttack from "./BTAttack"
import BTRoundBegin from "./BTRoundBegin"
import BTRoundEnd from "./BTRoundEnd"
import BaseBTNode from "./_BaseBTNode"
import Parallel from "./_Parallel"
import Priority from "./_Priority"
import Sequence from "./_Sequence"

class BevTreeFactory {

    private classMap: any = {}

    constructor() {
        this.classMap['Parallel'] = Parallel
        this.classMap['Priority'] = Priority
        this.classMap['Sequence'] = Sequence
        this.classMap['BTRoundBegin'] = BTRoundBegin
        this.classMap['BTAttack'] = BTAttack
        this.classMap['BTRoundEnd'] = BTRoundEnd
    }

    public newNode(type: string): BaseBTNode {
        const cls = this.classMap[type]
        return cls ? new cls() : null
    }
}

export const bevTreeFactory = new BevTreeFactory()
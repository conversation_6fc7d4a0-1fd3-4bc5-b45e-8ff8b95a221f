import { log } from "cc"
import NetworkModel from "./NetworkModel"
import { LoginType, NotifyType } from "../../common/constant/Enums"

/**
 * 用户模块
 */
@mc.addmodel('user')
export default class UserModel extends mc.BaseModel {

    private net: NetworkModel = null

    private localPreferenceMap: { [key: string]: any } = {} //本地偏好设置
    private tempPreferenceMap: { [key: string]: any } = {} //临时偏好设置

    private initTime: number = 0
    private uid: string = ''
    private sessionId: string = ''
    private createTime: number = 0
    private loginType: LoginType = LoginType.NONE //登陆类型
    private nickname: string = ''
    private roleId: number = 0 //当前的角色

    public onCreate() {
        this.net = this.getModel('net')
        this.localPreferenceMap = storageMgr.loadJson('preference_map') || {}
    }

    public init(data: any) {
        const now = this.initTime = Date.now()
        log('init user', data)
        this.uid = data.uid
        this.sessionId = data.sessionId
        this.createTime = data.createTime || 0
        this.loginType = data.loginType
        this.nickname = data.nickname || '???'
        this.roleId = data.roleId || 110001
        // 监听消息
        this.net.on('lobby/OnUpdateUserInfo', this.OnUpdateUserInfo, this)
    }

    public getUid() { return this.uid }
    public getNickname() { return this.nickname }
    public getRoleId() { return this.roleId }

    // ----------------------------------------- net listener function --------------------------------------------
    // 更新用户信息
    private OnUpdateUserInfo(data: { list: { type: NotifyType }[] }) {
        log('OnUpdateUserInfo', data.list)
        data.list.forEach(m => {
            const data = m['data_' + m.type]

        })
    }
}
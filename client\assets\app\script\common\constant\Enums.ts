
// 登陆类型
enum LoginType {
    NONE = 'none',
    GUEST = 'guest', //游客
    ACCOUNT = 'account', //账号
    WX = 'wx', //微信
    GOOGLE = 'google',
    APPLE = 'apple',
    FACEBOOK = 'facebook',
    TWITTER = 'twitter',
    LINE = 'line',
}

// 登录状态
enum LoginState {
    SUCCEED,            //成功
    FAILURE,            //登录失败
    NOT_ACCOUNT_TOKEN,  //本地还没有token 显示登录按钮
    VERSION_TOOLOW,     //版本过低
    VERSION_TOOTALL,    //版本过高
    BANACCOUNT_TIME,    //封禁时间
    QUEUE_UP,           //需要排队
    ENTER_GAME,         //有游戏数据
}

// 服务器的通知类型
enum NotifyType {
    NONE,
}

// 阵营
enum AnimalCamp {
    NONE,
    FRIENDLY,
    ENEMY,
}

// 动物状态
enum AnimalState {
    NONE,
    STAND, //待机 以下都表示战斗中
    ATTACK, //攻击
    HIT, //受击
}

export {
    LoginType,
    LoginState,
    NotifyType,
    AnimalCamp,
    AnimalState,
}
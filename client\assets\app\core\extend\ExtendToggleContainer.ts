/**
 * ToggleContainer 扩展方法
 */

import { SwihToggleCallback, Toggle, ToggleContainer } from "cc"

// 切换
ToggleContainer.prototype.Swih = function (val: string | number | SwihToggleCallback): Toggle[] {
    let name: string = '', cb: SwihToggleCallback = null
    if (typeof (val) === 'function') {
        cb = val
    } else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val)
    } else {
        return []
    }
    let arr: Toggle[] = []
    this.toggleItems.forEach((m: Toggle) => {
        const checked = cb ? !!cb(m) : (m.node.name === name)
        if (checked) {
            const event = this.checkEvents.shift()
            m.isChecked = true
            event && this.checkEvents.push(event)
            arr.push(m)
        } else if (m.isChecked) {
            m.isChecked = false
        }
    })
    return arr
}

// 切换tabs
ToggleContainer.prototype.Tabs = function (val: string | number | SwihToggleCallback): Toggle {
    let name = '', ret: Toggle = null, cb: SwihToggleCallback = null
    if (typeof (val) === 'function') {
        cb = val
    } else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val)
    } else {
        return ret
    }
    this.toggleItems.forEach((m: Toggle) => {
        const checked = cb ? !!cb(m) : (m.node.name === name)
        if (checked) {
            ret = m
            const event = this.checkEvents.shift()
            m.isChecked = true
            if (event) {
                event.emit([m, 'true'])
                this.checkEvents.push(event)
            }
        } else if (m.isChecked) {
            m.isChecked = false
        }
    })
    return ret
}
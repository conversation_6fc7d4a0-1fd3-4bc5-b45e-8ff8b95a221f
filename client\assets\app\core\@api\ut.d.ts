
declare namespace ut {

    // @ts-ignore
    import { Vec2, Vec3, Color, Component, Prefab, Node } from "cc";

    export var Time: {
        readonly Day: number;
        readonly Hour: number;
        readonly Minute: number;
        readonly Second: number;
    }

    export var MAX_VALUE: number;
    export var MIN_VALUE: number;

    export function now(): number;
    export function dateZeroTime(msd?: number): number;

    export function millisecondToString(msd: number): string;

    /**
     * 将一个毫秒数格式化 format('d:hh:mm:ss')
     * @param msd 
     * @param format 默认'mm:ss'
     */
    export function millisecondFormat(msd: number, format?: string): string;

    /**
     * 将一个秒格式化
     * @param val 
     * @param format 默认'mm:ss'
     */
    export function secondFormat(val: number, format?: string): string;

    /**
     * 将一个时间 format('yyyy-MM-dd hh:mm:ss')
     * @param format
     * @param msd
     */
    export function dateFormat(format: string, msd?: number): string;

    /**
     * 首字母变成大写
     * @param str 
     */
    export function initialUpperCase(str: string): string;

    /**
     * 将数字转换为String 中文
     * @param money 
     * @param num 
     */
    export function simplifyMoneyCh(money: number, num?: number): string;

    /**
     * 将数字转换为String 英文
     * @param money 
     * @param num 
     */
    export function simplifyMoneyEn(money: number, num?: number): string;

    export function simplifyMoney(money: number, num?: number): string;

    /**
     * 名字省略
     * @param name 
     * @param max 
     * @param extra 
     */
    export function nameFormator(name: string, max: number, extra?: string): string;

    /**
     * 获取字符串长度 一个汉字算2个长度
     * @param str 
     */
    export function getStringLen(str: string): number;

    /**
     * 将数字以逗号隔开
     * @param num 
     */
    export function formatNumberByComma(num: number): string;

    /**
     * 随机一个整数 包括min和max
     * @param min [最小值]
     * @param max [最大值]
     */
    export function random(min: number, max?: number): number;

    /**
     * 是否有概率
     * @param odds 概率值必须是100内的数字
     * @param mul 概率值倍数
     */
    export function chance(odds: number, mul?: number): boolean

    /**
     * 随机一个负数到正数的范围
     */
    export function randomRange(min: number, max: number): number;

    /**
     * 随机一个下标出来
     * @param len    [数组长度]
     * @param count  [需要随机的个数](可不填)
     * @param ignore [需要忽略的下标](可不填)
     */
    export function randomIndex(len: number, count?: number, ignore?: any);

    /**
     * 根据权重随机
     * @param arr 权重数组
     */
    export function randomIndexByWeight(arr: any[], key?: string | number);

    /**
     * 打乱数组
     * @param arr 数组
     */
    export function shuffleArray(arr: any[]): any[];

    /**
     * 新的获取角度
     * 以a为圆点开始顺时针方向旋转到b点的角度
     * @ 以圆的右边居中开始算 顺时针为负数 逆时针为正数
     * @param a [起点]
     * @param b [目标点]
     */
    export function getAngle(a: Vec2 | Vec3, b: Vec2 | Vec3): number;
    export function normAngle(angle: number): number;

    /**
     * Math.sin 返回Y坐标
     * @param angle 
     */
    export function sin(angle: number): number;

    /**
     * Math.cos 返回X坐标
     * @param angle 
     */
    export function cos(angle: number): number;

    /**
     * 根据角度和距离 获取坐标
     * @param angle 
     * @param dis 
     * @param out 
     */
    export function angleToPoint(angle: number, dis: number, out?: Vec2): Vec2;

    /**
    * 获取某个节点在某个节点里面的坐标
    * @param node 需要转换的节点
    * @param targetNode 要转换到的目标节点
    */
    export function convertToNodeAR(node: Node, targetNode: Node, out?: Vec2): Vec2;

    /**
    * 数字 字符串补0,根据长度补出前面差的0
    * @param num 需要补的数字
    * @param length 要补的长度默认为2
    */
    export function pad(num: number, length?: number): string;

    /**
     * 将一个数字 分解成多个类型的数字
     * @param num   [数字]
     * @param types [你想分解的类型列表 可不填]
     */
    export function decomposeNumberToTypes(num: number, types?: number[], out?: any): any;

    /**
     * 将一个字符串转换成向量
     * @param str 一个字符串必须满足以逗号隔开
     * @param separator 分隔符默认','
     */
    export function stringToVec2(str: string, separator?: string): Vec2;

    /**
     * 将一个字符串拆分为数组
     * @param str 
     * @param separator 默认|
     */
    export function stringToNumbers(str: string, separator?: string): number[];

    /**
     * 将一个常数变成1 并保留正负
     * @param val 
     */
    export function normalizeNumber(val: number): number;

    /**
     * 将一个数字转换为带正负符号的字符串
     * @param val 
     */
    export function numberToString(val: number): string;

    /**
     * 填充一个带参数的字符串
     * @param text 
     * @param params 
     */
    export function stringFormat(text: string, params: any[]): string;

    /**
     * 同步等待时间 (单位秒)
     * @param delay 
     */
    export function wait(delay: number, target?: Component): Promise<void>;

    /**
     * 同步等待时间 (单位毫秒)，用setTimeout实现
     * @param delay 
     */
    export function waitTimeout(delay: number): Promise<void>;

    /**
     * 用于代替setTimeout 内部使用scheduleOnce
     * @param cb 
     * @param delay 
     * @param target 
     */
    export function setTimeout(cb: Function, delay: number, target?: Component): any;
    export function clearTimeout(cb: Function, target?: Component): void;

    /**
     * 等待下一帧
     * @param frames (需要等待的帧数,默认1)
     */
    export function waitNextFrame(frames?: number, target?: Component): Promise<void>;

    /**
     * 锁
     * @param tag 
     * @param waitInterval (等待间隔, 默认0.1)
     */
    export function lock(tag: string, waitInterval?: number): Promise<void>;
    export function unlock(tag: string);

    /**
     * 读取 16 进制颜色
     * color.fromHEX("#FFFF33");
     * @param hexString 
     */
    export function colorFromHEX(hexString: string): Color;

    /**
     * 生成一个唯一ID
     */
    export function UID(): string;

    /**
     * 是否对象
     * @param o 
     */
    export function isObject(o: any): boolean;

    /**
     * 判断是否空对象
     * @param o 
     */
    export function isEmptyObject(o: any);

    /**
     * 拷贝对象
     * @param obj 
     */
    export function cloneObject(obj: any): any;

    /**
     * 深度拷贝对象
     * @param obj 
     */
    export function deepClone(obj: any, inDeep?: boolean): any;

    /**
     * 深度比较两个对象是否相等
     * @param x 
     * @param y 
     */
    export function compareObject(x: any, y: any): boolean;

    /**
     * 组装列表
     * @param arr 
     * @param datas 
     * @param item 
     * @param parent 
     * @param cb 
     */
    export function items(arr: Node[], datas: any[], item: Node | Prefab, parent: Node, cb: Function): void;

    /**
     * 循环值
     * @param val 
     * @param len [数组长度] 
     */
    export function loopValue(val: number, len: number): number;

    /**
     * 设置屏幕常亮
     * @param val 
     */
    export function setKeepScreenOn(val: boolean);

    /**
     * 将一个bool值转成1和0
     * @param val 
     */
    export function boolToNumber(val: boolean): number;

    /**
     * 对象给对象赋值
     * @param target 
     * @param value 
     * @param fields 
     */
    export function setValue(fields: string, data: any, target?: any): any;

    /**
     * http请求
     * @param method 
     * @param url 
     * @param data 
     * @param cb 
     */
    export function httpRequest(method: string, url: string, data?: any): Promise<{ status: number, data?: any }>;

    /**
     * 是否ios
     */
    export function isIos(): boolean;

    /**
     * 判断是安卓
     */
    export function isAndroid(): boolean;

    /**
     * 是否手机平台
     */
    export function isMobile(): boolean;

    /**
     * 是否微信游戏
     */
    export function isWechatGame(): boolean;

    /**
     * 是否qq游戏
     */
    export function isQQGame(): boolean;

    /**
     * 判断是否是小程序
     */
    export function isMiniGame(): boolean;

    /**
     * 获取随机字符串
     */
    export function getRandomString(len: number): string;

    /**
     * 创建一个数组
     * @param count 
     * @param val 
     */
    export function newArray(count: number, val?: any): any[];

    /**
     * 同步锁 同时调用的时候 只会执行第一个
     */
    export function syncLock(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor;

    /**
     * 同步等待 同时调用多个的时候 会等待上一个完成后继续下一个
     */
    export function syncWait(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor;

    /**
     * 获取浏览器参数
     */
    export function getBrowserParams(): { [key: string]: string };
    export function getBrowserParamByKey(key: string): string;

    /**
     * 是否在多边形里
     * @param point 
     * @param polygonPoints 
     */
    export function isInPolygon(point: Vec2, polygonPoints: Vec2[]): boolean;

    /**
     * 检测版本 a >= b 为true，反之则为false
     * @param a 
     * @param b 
     */
    export function checkVersion(a: string, b: string): boolean;

    /**
     * 监听游戏回到前台
     */
    export function waitGameShow(): Promise<void>;

    /**
     * 全局查找节点
     */
    export function findNode(path: string, count?: number): Promise<Node>;

    /**
     * 全局查找节点 
     */
    export function destroyNode(node: Node): boolean;

    /**
     * 时间差
     * @param start 开始时间
     * @param date 结束时间
     */
    export function timediff(start: number, date: string): number;
}
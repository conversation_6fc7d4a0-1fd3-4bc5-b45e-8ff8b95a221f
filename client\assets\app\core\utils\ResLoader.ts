import { Asset, assetManager, js, resources, SpriteFrame } from "cc"

/**
* 资源加载器 2.4.x
*/
class ResLoader {

    public debug: boolean = true //是否打印日志
    public error: boolean = true //是否打印错误日志

    // 远程资源列表
    private remoteAssetMap: Map<string, Asset> = new Map<string, Asset>()

    // 加载时间
    private loadTimeMap: Map<number, number> = new Map<number, number>()
    private __load_id: number = 0 // 加载ID
    private __load_urls: {} = {} //临时记录当前加载的资源
    private __temp_doesn_res: any = {}

    private getLoadId() {
        return ++this.__load_id
    }

    private getLoadUrlCount(url: string): number {
        return this.__load_urls[url] ?? 0
    }

    // 改变加载次数
    private changeLoadUrlCount(url: string, val: number) {
        const count = this.__load_urls[url], cnt = (count ?? 0) + val
        if (cnt > 0) {
            this.__load_urls[url] = cnt
        } else if (count !== undefined) {
            delete this.__load_urls[url]
        }
        return cnt
    }

    // 放弃加载
    public giveupLoad(url: string) {
        delete this.__load_urls[url]
    }

    public printError(msg: any | string, ...subst: any[]) {
        if (this.debug && this.error) {
            logger.error(msg, ...subst)
        }
    }

    public printInfo(msg: any | string, ...subst: any[]) {
        if (this.debug) {
            logger.info(msg, ...subst)
        }
    }

    // 解析参数
    private makeLoadResArgs(params: any[]): any {
        const len = params.length
        if (len < 1 || typeof (params[0]) !== 'string') {
            this.printError('makeLoadResArgs error', params)
            return { url: '' }
        }
        const args: any = { url: params[0] }
        for (let i = 1; i < len; i++) {
            const param = params[i]
            if (i === 1 && js.isChildClassOf(param, Asset)) {
                args.type = param
            } else if (typeof (param) === 'function') {
                args.onProgess = param
            }
        }
        return args
    }

    // public async loadRes(url: string): Promise<Asset>;
    // public async loadRes(url: string, onProgess: Function): Promise<Asset>;
    // public async loadRes(url: string, type: typeof Asset): Promise<Asset>;
    // public async loadRes(url: string, type: typeof Asset, onProgess: Function): Promise<Asset>;
    public async loadRes(...params: any): Promise<any> {
        const id = this.getLoadId()
        this.debug && this.loadTimeMap.set(id, Date.now())
        const asset = await this.load(...params)
        return this.loadResComplete(id, params[0], asset)
    }
    public async load(...params: any) {
        const { url, type, onProgess } = this.makeLoadResArgs(params)
        return new Promise<any>(resolve => {
            let res = resources.get(url, type)
            if (!res) {
                return this.__load(url, type, onProgess, resolve)
            } else if (res.refCount > 0) {
                return resolve(res)
            } else {
                ut.waitNextFrame().then(() => this.__load(url, type, onProgess, resolve))
            }
        })
    }
    private __load(url: string, type: typeof Asset, onProgess: any, onComplete: Function) {
        if (this.__temp_doesn_res[url]) {
            return onComplete(null)
        }
        this.changeLoadUrlCount(url, 1)
        const _url = type === SpriteFrame ? url + '/spriteFrame' : url
        resources.load(_url, type, onProgess, (err, asset) => {
            if (!err) {
                this.changeLoadUrlCount(url, -1)
                onComplete(asset)
            } else if (err.message.includes(`Bundle resources doesn't contain`)) {
                this.printError('loadRes error -> ' + url, err.message)
                this.changeLoadUrlCount(url, -1)
                this.__temp_doesn_res[url] = true //记录没有的路径
                onComplete(null)
            } else if (this.getLoadUrlCount(url) > 0) {
                this.printInfo('loadRes error! try reload. ' + url, err.message)
                ut.wait(0.1).then(() => {
                    if (this.getLoadUrlCount(url) > 0) {
                        this.__load(url, type, onProgess, onComplete) //尝试重新加载
                    } else {
                        onComplete(null)
                    }
                })
            } else {
                onComplete(null)
            }
        })
    }

    // public async loadResDir(url: string): Promise<Asset[]>;
    // public async loadResDir(url: string, onProgess: Function): Promise<Asset[]>;
    // public async loadResDir(url: string, type: typeof Asset): Promise<Asset[]>;
    // public async loadResDir(url: string, type: typeof Asset, onProgess: Function): Promise<Asset[]>;
    public async loadResDir(...params: any): Promise<any[]> {
        const id = this.getLoadId()
        this.debug && this.loadTimeMap.set(id, Date.now())
        const assets = await this.loadDir(...params)
        return assets.map(m => this.loadResComplete(id, params[0] + '/' + m.name, m))
    }
    public async loadDir(...params: any) {
        const { url, type, onProgess } = this.makeLoadResArgs(params)
        return new Promise<any[]>(resolve => this.__loadDir(url, type, onProgess, resolve))
    }
    private __loadDir(url: string, type: typeof Asset, onProgess: any, onComplete: Function) {
        resources.loadDir(url, type, onProgess, (err, assets) => {
            if (err) {
                if (err.message.includes(`Bundle resources doesn't contain`)) {
                    this.printError('loadRes error -> ' + url, err.message)
                    onComplete([])
                } else {
                    this.printInfo('loadRes error! try reload. ' + url, err.message)
                    ut.wait(0.1).then(() => this.__loadDir(url, type, onProgess, onComplete)) //尝试重新加载
                }
            } else {
                onComplete(assets)
            }
        })
    }

    // 加载JPG
    public async loadRemote(url: string, ext: string) {
        return new Promise<any>(resolve => {
            let res = this.remoteAssetMap.get(url)
            if (res) {
                return resolve(res)
            }
            this.__loadRemote(url, ext, resolve)
        })
    }
    private __loadRemote(url: string, ext: string, onComplete: Function) {
        assetManager.loadRemote(url, { ext: ext }, (err, asset) => {
            if (err) {
                this.printError('loadRes error -> ' + url, err.message)
                onComplete(null)
            } else {
                this.remoteAssetMap.set(url, asset)
                onComplete(asset)
            }
            // 底层默认有重新加载 这里就不重新加载了
            // if (!err) {
            //     this.remoteAssetMap.set(url, asset)
            //     onComplete(asset)
            // } else if (!err.message.includes('download failed')) {
            //     this.printError('loadRes error -> ' + url, err.message)
            //     onComplete(null)
            // } else {
            //     this.printInfo('loadRes error! try reload. ' + url, err.message)
            //     ut.wait(0.1).then(() => this.__loadRemote(url, ext, onComplete)) //尝试重新加载
            // }
        })
    }

    // 加载资源完成
    public loadResComplete(id: number, url: string, asset: Asset) {
        if (asset) {
            // 添加引用
            asset.addRef()
            // 打印
            if (this.debug) {
                const now = Date.now()
                const time = now - (this.loadTimeMap.get(id) || now)
                this.loadTimeMap.delete(id)
                this.printInfo(`loadRes -> ${url} [${asset.refCount}] ${time}ms`)
            }
        }
        return asset
    }

    // 释放资源
    public releaseRes(url: string, type?: typeof Asset) {
        let asset = resources.get(url, type), isRemote = false
        if (!asset) {
            asset = this.remoteAssetMap.get(url)
            isRemote = true
        }
        if (!asset) {
            this.printError(`releaseRes asset is null ${url}`)
            return 0
        }
        // 减少引用并尝试自动释放
        // @ts-ignore
        asset.decRef(false)
        const refCount = asset.refCount
        if (refCount <= 0) {
            assetManager.releaseAsset(asset)
            // 如果是远程资源 还要删除缓存
            if (isRemote) {
                this.remoteAssetMap.delete(url)
            }
        }
        // 打印
        this.printInfo(`releaseRes -> ${url} [${refCount}]`)
        return refCount
    }

    // 释放所有引用
    public releaseAsset(url: string, type?: typeof Asset) {
        let asset = resources.get(url, type), isRemote = false
        if (!asset) {
            asset = this.remoteAssetMap.get(url)
            isRemote = true
        }
        if (!asset) {
            this.printError(`releaseAsset asset is null ${url}`)
            return
        }
        // 直接强行释放
        assetManager.releaseAsset(asset)
        // 如果是远程资源 还要删除缓存
        if (isRemote) {
            this.remoteAssetMap.delete(url)
        }
        // 打印
        this.printInfo(`releaseAsset -> ${url} [0]`)
    }

    public clean() {
        this.remoteAssetMap.clear()
        this.loadTimeMap.clear()
        this.__load_id = 0
        this.__load_urls = {}
        this.__temp_doesn_res = {}
    }
}

export const loader = new ResLoader()
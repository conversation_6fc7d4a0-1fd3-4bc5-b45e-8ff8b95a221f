import { log, error } from "cc"
import CoreEventType from "../event/CoreEventType"

class Logger {

    private _open: boolean = true //是否打开

    public get open() {
        return this._open
    }

    public set open(val: boolean) {
        this._open = val
    }

    public info(msg: any | string, ...subst: any[]) {
        if (!this._open) {
            return
        }
        const text = this.wrapLogger(msg, subst)
        if (!ut.isMobile()) {
            const fu = console.log || log
            fu.call(this, text)
        } else {
            console.log(text)
        }
        eventCenter.emit(CoreEventType.MVC_LOGGER_PRINT, 'info', text)
    }

    public error(msg: any | string, ...subst: any[]) {
        if (!this._open) {
            return
        }
        const text = this.wrapLogger(msg, subst)
        if (!ut.isMobile()) {
            const fu = console.error || error
            fu.call(this, text)
        } else {
            console.error(text)
        }
        eventCenter.emit(CoreEventType.MVC_LOGGER_PRINT, 'error', text)
    }

    private wrapLogger(msg: any | string, subst: any[]): string {
        return '[' + ut.dateFormat('h:mm:ss') + '] ' + this.formatter(msg) + ' ' + subst.join2(m => this.formatter(m), ' ')
    }

    private formatter(val: any): string {
        if (val === null) {
            return 'null'
        } else if (val === undefined) {
            return 'undefined'
        } else if (Array.isArray(val)) {
            return '[' + val.join2(m => this.formatter(m), ',') + ']'
        } else if (typeof (val) === 'object') {
            return this.warpObject(val)
        } else {
            return String(val)
        }
    }

    private warpObject(obj: any): string {
        try {
            if (obj.__classname__) {
                return `${obj.__classname__} { name: ${obj.name} }`
            }
            return JSON.stringify(obj)
        } catch (error) {
            return '无法解析'
        }
    }
}

window['logger'] = new Logger()
import { __private, Asset, AudioClip, Font, js, JsonAsset, Material, Prefab, SpriteFrame, Texture2D } from "cc";
import CoreEventType from "../event/CoreEventType"
import { loader } from "../utils/ResLoader"

type TempAssetQueueInfo = {
    cb: Function;
    tag: string;
}

// 临时资源结构
type TempAssetData = {
    name: string;
    asset: Asset;
    url: string;
    type: typeof Asset;
    refs: { key: string, count: number }[]; //引用列表
}

/**
 * 资源管理
 * 
 * 目录结构
 * 
 * common
 *      image
 *      json
 *      prefab
 *      audio
 *      font
 *          cn
 *          en
 * tmp
 *      image
 *      prefab
 *      audio
 */
class AssetsMgr {

    private prefabs = new Map<string, Prefab>()// 公共预制体
    private images = new Map<string, SpriteFrame>()// 永久存在图片
    private audios = new Map<string, AudioClip>()// 公共声音
    private jsons = new Map<string, JsonConfData>()// 公共配置文件
    private fonts = new Map<string, Map<string, Font>>()// 公共字体
    private materials = new Map<string, Material>()// 公共材质

    private temps = new Map<string, TempAssetData>()// 临时缓存资源
    private loadTempQueueMap = new Map<string, TempAssetQueueInfo[]>() //加载队列 防止一个资源同时被多个加载
    private isLoadFont: boolean = false //是否加载font中
    private lastLoadFontLang: string = ''

    private __onProgessCallback: Function = null
    private __totalProgess: number = 0
    private __tempProgess: any = null
    private __curPercent: number = 0

    public async init(onProgess?: Function) {
        this.__onProgessCallback = onProgess
        this.temps.clear()
        this.__curPercent = 0
        this.__tempProgess = {}
        const arr = [this.loadJsons(), this.loadImages(), this.loadPrefab(), this.loadAudio(), this.loadMaterial(), this.loadFont()]
        this.__totalProgess = 1 / arr.length
        const debug = this.debug
        this.debug = false
        await Promise.all(arr)
        this.debug = debug
        this.__onProgessCallback = null
        this.__tempProgess = null
    }

    public clean() {
        this.prefabs.clear()
        this.images.clear()
        this.audios.clear()
        this.jsons.clear()
        this.fonts.clear()
        this.materials.clear()
        this.loadTempQueueMap.clear()
        this.temps.clear()
    }

    public set debug(val: boolean) { loader.debug = val }
    public get debug() { return loader.debug }

    // 初始化的加载进度
    private onInitLoadProgess(key: string, done: number, total: number) {
        if (!this.__onProgessCallback || !total) {
            return
        }
        let last = this.__tempProgess[key]
        if (last === undefined) {
            last = this.__tempProgess[key] = 0
        }
        let curr = done / total
        if (last >= curr) {
            return
        }
        this.__tempProgess[key] = curr
        const diff = Math.max(curr - last, 0) * this.__totalProgess
        this.__curPercent += diff
        this.__onProgessCallback(this.__curPercent)
    }

    // 初始json文件配置
    private async loadJsons() {
        const assets: JsonAsset[] = await loader.loadResDir('common/json', JsonAsset, (done: number, total: number) => this.onInitLoadProgess('json', done, total))
        assets.forEach(m => this.addJsonAsset(m))
    }
    private addJsonAsset(m: JsonAsset) {
        const dataIdMap = {}
        if (m.json.length > 0 && m.json[0]['id'] !== undefined) {
            m.json.forEach(m => dataIdMap[m.id] = m)
        }
        this.jsons.set(m.name, {
            // @ts-ignore
            datas: m.json,
            dataIdMap: dataIdMap,
            getById(id: string | number) {
                return this.dataIdMap[id]
            },
            get(key: string, value: any) {
                return this.datas.filter(m => m[key] === value)
            }
        })
    }

    // 初始通用图片集
    private async loadImages() {
        const assets: SpriteFrame[] = await loader.loadResDir('common/image', SpriteFrame, (done: number, total: number) => this.onInitLoadProgess('image', done, total))
        assets.forEach(m => this.images.set(m.name, m))
    }

    // 初始化预制体
    private async loadPrefab() {
        const assets: Prefab[] = await loader.loadResDir('common/prefab', Prefab, (done: number, total: number) => this.onInitLoadProgess('prefab', done, total))
        assets.forEach(m => this.prefabs.set(m.name, m))
    }

    // 初始化声音
    private async loadAudio() {
        const assets: AudioClip[] = await loader.loadResDir('common/audio', AudioClip, (done: number, total: number) => this.onInitLoadProgess('audio', done, total))
        assets.forEach(m => this.audios.set(m.name, m))
    }

    // 材质
    private async loadMaterial() {
        const assets: Material[] = await loader.loadResDir('common/material', Material, (done: number, total: number) => this.onInitLoadProgess('material', done, total))
        assets.forEach(m => this.materials.set(m.name, m))
    }

    // 字体
    private async loadFont(lang?: string) {
        lang = lang || mc.lang
        if (this.lastLoadFontLang === lang) {
            return
        }
        const preLang = this.lastLoadFontLang
        this.lastLoadFontLang = lang
        const assets: Font[] = await loader.loadResDir('common/font/' + lang, Font, (done: number, total: number) => this.onInitLoadProgess('font', done, total))
        const map = this.getFontMap(lang)
        assets.forEach(m => map.set(m.name, m))
        // 清理上一个字体
        if (preLang && preLang !== lang) {
            const obj = this.fonts.get(preLang)
            if (obj) {
                obj.forEach(m => m.decRef())
                obj.clear()
                this.fonts.delete(preLang)
            }
        }
    }

    private getFontMap(lang?: string) {
        lang = lang || mc.lang
        let map = this.fonts.get(lang)
        if (!map) {
            map = new Map<string, Font>()
            this.fonts.set(lang, map)
        }
        return map
    }

    // 切换语言资源
    public async changeLangJson(lang: string) {
        if (this.isLoadFont) {
            return
        } else if (!this.fonts.has(lang)) {
            this.isLoadFont = true
            mc.lockTouch('change_lang_json')
            eventCenter.emit(CoreEventType.LOADING_WAIT_BEGIN)
            await this.loadFont(lang)
            eventCenter.emit(CoreEventType.LOADING_WAIT_END)
            mc.unlockTouch('change_lang_json')
        }
        this.isLoadFont = false
        eventCenter.emit(CoreEventType.LANGUAGE_CHANGED, lang)
    }

    // 获取图片
    public getImage(key: string): SpriteFrame {
        return this.images.get(key)
    }

    // 获取json配置
    public getJson(key: string): JsonConfData {
        return this.jsons.get(key)
    }
    public getJsonData(key: string, id: string | number): any {
        return this.getJson(key)?.getById(id)
    }

    // 获取预制体
    public getPrefab(key: string): Prefab {
        return this.prefabs.get(key)
    }

    // 获取声音
    public getAudio(key: string): AudioClip {
        return this.audios.get(key)
    }

    // 获取材质
    public getMaterial(key: string): Material {
        return this.materials.get(key)
    }

    // 获取字体
    public getFont(key: string): Font {
        return this.getFontMap()?.get(key)
    }

    // 根据key获取文本
    public lang(key: string, ...params: any[]) {
        if (!key) {
            return ''
        }
        const lang = mc.lang
        const _params = []
        params?.forEach(m => Array.isArray(m) ? _params.pushArr(m) : _params.push(m))
        const [name, id] = key.split('.')
        const json = this.getJsonData(name, id) || {}
        const val = json[lang]
        if (val !== undefined) {
            return ut.stringFormat(val, this.updateLangParams(_params, lang))
        }
        return ut.stringFormat(key, this.updateLangParams(_params, lang))
    }

    // 刷新参数
    public updateLangParams(params: any[], lang?: string) {
        if (!params) {
            return []
        }
        lang = lang || mc.lang
        return params.map(m => {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                const [name, id] = m.split('.')
                const json = this.getJsonData(name, id) || {}
                const val = json[lang]
                return val !== undefined ? val : m
            }
            return m
        })
    }

    // 根据类型获取文件夹名字
    private makeTypeName(type: any): string {
        const name = js.getClassName(type)
        if (name === 'cc.Prefab') {
            return 'prefab'
        } else if (name === 'cc.SpriteFrame') {
            return 'image'
        } else if (name === 'cc.AudioClip') {
            return 'audio'
        } else if (name === 'cc.Skeleton') {
            return 'spine'
        } else if (name === 'cc.JsonAsset') {
            return 'json'
        } else if (name === 'cc.Font') {
            return 'font'
        } else if (name === 'cc.Material') {
            return 'material'
        } else if (name === 'cc.AnimationClip') {
            return 'animation'
        } else if (name === 'cc.Asset') {
            return 'asset'
        }
        return ''
    }

    // 加载核心资源
    public async loadCommonRes(name: string, type: typeof Asset): Promise<any> {
        const at = this.makeTypeName(type)
        const assetMap: Map<string, any> = this[at + 's']
        if (!assetMap) {
            return null
        }
        const isFont = at === 'font'
        const it = isFont ? this.getFont(name) : assetMap.get(name)
        if (it) {
            return it
        }
        return new Promise(resolve => {
            const url = isFont ? 'common/' + at + '/' + mc.lang + '/' + name : 'common/' + at + '/' + name
            loader.load(url, type).then(asset => {
                if (!asset) {
                } else if (isFont) {
                    this.getFontMap()?.set(asset.name, asset)
                } else if (at === 'json') {
                    this.addJsonAsset(asset)
                } else {
                    assetMap.set(asset.name, asset)
                }
                resolve(asset)
            })
        })
    }

    // 加载临时资源
    public async loadTempRes<T extends Asset>(name: string, type: __private.__types_globals__Constructor<T>, tag?: string, progress?: ProcessCallback): Promise<T> {
        const it = this.temps.get(name)
        if (it) {
            return this.addTempResRefs(it, tag)
        }
        return new Promise(resolve => {
            const url = 'tmp/' + this.makeTypeName(type) + '/' + name
            // 放入队列
            const queues = this.loadTempQueueMap.get(url)
            if (queues) {
                return queues.push({ cb: resolve, tag: tag })
            }
            this.loadTempQueueMap.set(url, [{ cb: resolve, tag: tag }])
            // 开始加载
            loader.load(url, type, progress).then(asset => {
                const queues2 = this.loadTempQueueMap.get(url)
                if (!queues2) {
                    return loader.printError('加载错误 not funcs? url=' + url)
                } else {
                    // 先添加引用
                    if (asset) {
                        queues2.forEach(m => this.addTempRes(name, url, asset, type, m.tag))
                    }
                    // 再回调回去 先添加引用是防止在回调的时候被释放了
                    queues2.forEach(m => m.cb(asset))
                }
                this.loadTempQueueMap.delete(url)
            })
        })
    }

    // 加载临时资源 文件夹
    public async loadTempRseDir<T extends Asset>(key: string, type: __private.__types_globals__Constructor<T>, tag?: string, progress?: ProcessCallback): Promise<T[]> {
        const head = 'tmp/' + this.makeTypeName(type) + '/'
        const assets = await loader.loadDir(head + key, type, progress)
        for (let i = 0, l = assets.length; i < l; i++) {
            const asset: Asset = assets[i]
            const name = key + '/' + asset.name
            this.addTempRes(name, head + name, asset, type, tag)
        }
        return assets
    }

    // 加载远程图片
    public async loadRemote(url: string, ext: string, tag?: string): Promise<SpriteFrame> {
        if (!url) {
            return null
        }
        let it = this.temps.get(url)
        if (it) {
            return this.addTempResRefs(it, tag) as SpriteFrame
        }
        const asset = await loader.loadRemote(url, ext)
        if (!asset) {
            return null
        } else if (!(asset instanceof Texture2D)) {
            loader.printError('加载错误 格式不对 url=' + url)
            return null
        }
        // 添加临时资源
        const sf = new SpriteFrame()
        sf.texture = asset
        this.addTempRes(url, url, sf, SpriteFrame, tag)
        return sf
    }

    // 添加临时资源
    private addTempRes(name: string, url: string, asset: Asset, type: any, tag: string) {
        let it = this.temps.get(name)
        if (!it) {
            // 添加一次资源本身的引用计数
            asset.addRef()
            // 计入缓存
            it = { name: name, asset: asset, url: url, type: type, refs: [] }
            this.temps.set(name, it)
        }
        return this.addTempResRefs(it, tag)
    }

    // 添加临时资源引用
    private addTempResRefs(it: TempAssetData, tag: string): any {
        tag = tag || ''
        let ref = it.refs.find(m => m.key === tag)
        if (!ref) {
            ref = it.refs.add({ key: tag, count: 0 })
        }
        ref.count += 1
        // 打印
        loader.printInfo(`loadTempRes -> ${it.url} [${ref.count}] <${tag}>`)
        return it.asset
    }

    // 删除临时资源引用
    private removeTempResRef(it: TempAssetData, tag: string) {
        const ref = it.refs.remove('key', tag)
        if (it.refs.length === 0) {
            this.temps.delete(it.name)
            loader.releaseRes(it.url, it.type)
        } else if (ref) {
            loader.printInfo(`removeTempRes -> ${it.url} [0] <${tag}>`)
        }
    }

    // 释放临时资源
    public releaseTempRes(name: string, tag?: string) {
        const it = this.temps.get(name)
        if (!it) {
            return loader.printInfo('try release null res[' + name + ']')
        }
        tag = tag || ''
        const ref = it.refs.find(m => m.key === tag)
        if (!ref) {
            return loader.printError('release error not ref[' + tag + '] at ' + it.url)
        }
        ref.count -= 1
        if (ref.count <= 0) {
            this.removeTempResRef(it, tag)
        } else {
            loader.printInfo(`removeTempRes -> ${it.url} [${ref.count}] <${tag}>`)
        }
    }

    // 强行释放资源
    public releaseTempAsset(name: string) {
        const it = this.temps.get(name)
        if (!it) {
            return loader.printInfo('try release null res[' + name + ']')
        }
        this.temps.delete(name)
        loader.releaseAsset(it.url, it.type)
    }

    // 释放所有标记的临时资源
    public releaseTempResByTag(tag: string) {
        this.temps.forEach(it => this.removeTempResRef(it, tag))
    }

    // 加载一次性资源 不缓存 
    // 注意：需要和releaseOnceRes成对使用
    public async loadOnceRes(name: string, type: typeof Asset) {
        const url = 'tmp/' + this.makeTypeName(type) + '/' + name
        const asset = await loader.load(url, type)
        asset && asset.addRef()
        return asset
    }

    public releaseOnceRes(name: string, type: typeof Asset) {
        const url = 'tmp/' + this.makeTypeName(type) + '/' + name
        loader.releaseRes(url, type)
    }
}

window['assetsMgr'] = new AssetsMgr()

/**
 * EditBox扩展方法
 */

import { EditBox } from "cc"
import BaseLocale from "../base/BaseLocale"

EditBox.prototype.setPlaceholder = function (key: string, font: string, ...params: any[]) {
    const locale = this.Component(BaseLocale)
    if (locale) {
        return locale.setKey(key, ...params)
    } else if (font) {
        this.placeholderLabel.font = assetsMgr.getFont(font)
    }
    this.placeholder = assetsMgr.lang(key, ...params)
}

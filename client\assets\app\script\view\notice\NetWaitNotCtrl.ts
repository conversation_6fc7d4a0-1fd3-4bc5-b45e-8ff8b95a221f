import { _decorator, log, Node } from "cc";
import NetEvent from "../../common/event/NetEvent";
import { viewHelper } from "../../common/helper/ViewHelper";
const { ccclass } = _decorator;

@ccclass
export default class NetWaitNotCtrl extends mc.BaseNoticeCtrl {

    //@autocode property begin
	private maskNode_: Node = null // path://mask_n
	private rootNode_: Node = null // path://root_n
	//@end

    private opening: boolean = false
    private delay: number = 1 //延迟多少秒显示画面
    private hideTime: number = 30 //延迟多少秒后强行关闭界面
    private elapsed: number = 0

    public listenEventMaps() {
        return [
            { [NetEvent.NET_REQ_BEGIN]: this.onEventOpen },
            { [NetEvent.NET_REQ_END]: this.onEventHide },
        ]
    }

    public async onCreate() {
        this.maskNode_.active = false
        this.rootNode_.active = false
        this.node.zIndex = 1
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
	//@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onEventOpen(delay: number) {
        if (this.opening) {
            return
        }
        this.open()
        this.opening = true
        this.maskNode_.active = true
        this.rootNode_.active = false
        this.delay = delay ?? 1
        this.elapsed = 0
    }

    private onEventHide() {
        this.hide()
        this.opening = false
        this.maskNode_.active = false
        this.rootNode_.active = false
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    update(dt: number) {
        if (!this.opening) {
            return
        }
        this.elapsed += dt
        if (this.elapsed >= this.delay && this.elapsed < this.hideTime) {
            this.elapsed += this.hideTime
            this.rootNode_.active = true
        }
        if (this.elapsed - this.hideTime >= this.hideTime) {
            if (mc.currWindName !== 'login') { // 登录界面无用
                viewHelper.showMessageBox('login.net_timeout')
            }
            this.onEventHide()
        }
    }
}


enum BTType {
    NONE = 'none',
    COMPOSITE = 'composite', //组合节点
    DECORATOR = 'decorator', //装饰节点
    ACTION = 'action', //动作节点
    CONDITION = 'condition', //条件节点
}

enum BTState {
    SUCCESS = 1, //成功
    FAILURE = 2, //失败
    RUNNING = 3, //运行中
    ERROR = 4, //错误
}

// 配置
/**
 * Priority
 *          Sequence
 *                  BTRoundBegin //回合开始
 *                  BTAttack //攻击
 *                  BTRoundEnd //回合结束
 *          BTRoundEnd
 */
const BEHAVIOR_CONFIG = {
    100001: { type: 'composite', cls: 'Priority', children: [100002, 100004], },
    100002: { type: 'composite', cls: 'Sequence', children: [100003, 100005, 100004], },
    100003: { type: 'composite', cls: 'BTRoundBegin', children: [], }, //回合开始
    100004: { type: 'composite', cls: 'BTRoundEnd', children: [], }, //回合结束
    100005: { type: 'composite', cls: 'BTAttack', children: [], }, //攻击
}
const BEHAVIOR_FIRST_ID = 100001

export {
    BTType,
    BTState,
    BEHAVIOR_CONFIG,
    BEHAVIOR_FIRST_ID,
}